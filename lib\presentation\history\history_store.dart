import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:mek_data_class/mek_data_class.dart';
import 'package:mobx/mobx.dart';

import '../../app/domain/errors.dart';
import '../../app/domain/path.dart';
import '../../app/persistence/data_repository.dart';
import '../../app/util/json_serializer.dart';
import '../../app/util/observable_utils.dart';
import '../domain/side.dart';
import './history.dart';

part '.gen/history_store.g.dart';

class HistoryStore extends HistoryStoreBase with _$HistoryStore {
  HistoryStore._();

  static Future<HistoryStore> create(DataRepository dataRepository, Side side, {HistoryStoreStateSnapshot? defaultHistory}) async {
    final historyStore = HistoryStore._();
    historyStore._disposer = await dataRepository.register<HistoryStoreStateSnapshot>(
      name: side.name,
      createDefault: defaultHistory != null ? () => defaultHistory : historyStore._createDefault,
      snapshot: historyStore.snapshot,
      restore: historyStore._restore,
      serializer: HistoryStoreStateSnapshot.serializer,
    );
    return historyStore;
  }

  @visibleForTesting
  static Future<HistoryStoreStateSnapshot> readSnapshot(DataRepository dataRepository, Side side) async {
    // ignore: invalid_use_of_visible_for_testing_member
    return await dataRepository.readSnapshot<HistoryStoreStateSnapshot>(
      name: side.name,
      serializer: HistoryStoreStateSnapshot.serializer,
    );
  }

  HistoryStoreStateSnapshot _createDefault() => HistoryStoreStateSnapshot([History.single(RawPath.currentWorkingDir)]);

  @visibleForTesting
  HistoryStoreStateSnapshot snapshot() => HistoryStoreStateSnapshot(histories.toList(), selectedTabIndex: _currentIndex);

  void _restore(HistoryStoreStateSnapshot snapshot) {
    if (snapshot.tabs.isEmpty) {
      throw AppException.invalidPersistedState("histories cannot be empty!");
    }
    if (snapshot.selectedTabIndex >= snapshot.tabs.length) {
      throw AppException.invalidPersistedState("selectedIndex cannot be greater than histories length!");
    }

    histories.replace(snapshot.tabs);
    setCurrentIndex(min(snapshot.selectedTabIndex, histories.length - 1));
  }
}

abstract class HistoryStoreBase with Store {
  HistoryStoreBase();

  final ObservableList<History> histories = ObservableList();

  late VoidCallback _disposer;

  @readonly
  int _currentIndex = 0;

  @computed
  History get current => histories[_currentIndex];

  @action
  void cloneCurrent() => _addAndSetCurrent(current.clone());

  @action
  void addWithPath(RawPath dir) => _addAndSetCurrent(History.single(dir));

  @action
  void addWithFocusedPath(RawPath path) {
    addWithPath(path.parent!);
    current.setFocusedPath(path, 0);
  }

  @action
  bool setCurrentHistoryToAnyWhichContainsPathAndFocus(RawPath path) {
    final history = setCurrentHistoryToAnyWhichContainsPath(path.parent!);
    if (history != null) {
      history.setFocusedPath(path, 0);
    }
    return history != null;
  }

  @action
  History? setCurrentHistoryToAnyWhichContainsPath(RawPath dir) {
    final index = histories.indexWhere((element) => element.current.dir == dir);
    if (index != -1) {
      setCurrentIndex(index);
      return histories[index];
    }
    return null;
  }

  @action
  void _addAndSetCurrent(History history) {
    histories.add(history);
    setCurrentIndex(histories.length - 1);
  }

  @action
  void setCurrentIndex(int currentIndex) {
    if (currentIndex >= histories.length) {
      throw Exception("Invalid history index: $currentIndex, max=${histories.length}");
    }
    if (_currentIndex != currentIndex) {
      _currentIndex = currentIndex;
    }
  }

  @action
  void delete(int index) {
    if (histories.length <= 1) return;
    histories.removeAt(index);
    if (_currentIndex == histories.length) {
      setCurrentIndex(_currentIndex - 1);
    }
  }

  @action
  void deleteCurrent() => delete(_currentIndex);

  @visibleForTesting
  void dispose() => _disposer();
}

@DataClass()
@JsonSerializable()
class HistoryStoreStateSnapshot with _$HistoryStoreStateSnapshot {
  const HistoryStoreStateSnapshot(this.tabs, {this.selectedTabIndex = 0});

  factory HistoryStoreStateSnapshot.single(RawPath path, {RawPath? focusedPath, int focusedRowIndex = 0}) =>
      HistoryStoreStateSnapshot([History.single(path, focusedPath: focusedPath, focusedRowIndex: focusedRowIndex)], selectedTabIndex: 0);

  final List<History> tabs;
  final int selectedTabIndex;

  static final serializer = JsonSerializer<HistoryStoreStateSnapshot>(
    toJson: (instance) => _$HistoryStoreStateSnapshotToJson(instance),
    fromJson: (json) => _$HistoryStoreStateSnapshotFromJson(json),
  );
}
