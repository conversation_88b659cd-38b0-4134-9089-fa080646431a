import 'package:flutter/services.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:mek_data_class/mek_data_class.dart';

import '../../app/util/json_serializer.dart';
import '../../app/util/log_utils.dart';
import 'command_id.dart';

part '.gen/keybind.g.dart';

/// Represents a keyboard binding for a command
@DataClass()
@JsonSerializable()
class KeyBind with _$KeyBind {
  /// The command ID this keybind is associated with
  final CommandId commandId;

  /// The key combination as a string (e.g., 'ctrl+shift+p')
  final String key;

  /// Optional expression that determines when this keybind is active.
  /// Unused for now.
  final String? when;

  /// The parsed key combination
  @JsonKey(includeFromJson: false, includeToJson: false)
  final LogicalKeyboardKey? _primaryKey;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool _isCtrl;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool _isShift;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool _isAlt;

  KeyBind({
    required this.commandId,
    required this.key,
    this.when,
  })  : _primaryKey = _parseKey(key),
        _isCtrl = key.toLowerCase().contains('ctrl'),
        _isShift = key.toLowerCase().contains('shift'),
        _isAlt = key.toLowerCase().contains('alt');

  /// Check if this keybind matches the given key
  bool matches(KeyEvent event) {
    // Check if the key combination matches
    if (event is! KeyDownEvent) return false;

    final isCtrlPressed = HardwareKeyboard.instance.isControlPressed;
    final isShiftPressed = HardwareKeyboard.instance.isShiftPressed;
    final isAltPressed = HardwareKeyboard.instance.isAltPressed;
    return event.logicalKey == _primaryKey && isCtrlPressed == _isCtrl && isShiftPressed == _isShift && isAltPressed == _isAlt;
  }

  /// Parse a key string into a LogicalKeyboardKey
  static LogicalKeyboardKey? _parseKey(String keyString) {
    // Remove modifiers to get the primary key
    final cleanKey = keyString.toLowerCase().replaceAll('ctrl+', '').replaceAll('shift+', '').replaceAll('alt+', '').trim();

    // Map common key names to LogicalKeyboardKey
    switch (cleanKey) {
      case 'p':
        return LogicalKeyboardKey.keyP;
      case 'f':
        return LogicalKeyboardKey.keyF;
      case 'c':
        return LogicalKeyboardKey.keyC;
      case 'x':
        return LogicalKeyboardKey.keyX;
      case 'up':
        return LogicalKeyboardKey.arrowUp;
      case 'down':
        return LogicalKeyboardKey.arrowDown;
      case 'left':
        return LogicalKeyboardKey.arrowLeft;
      case 'right':
        return LogicalKeyboardKey.arrowRight;
      case 'enter':
        return LogicalKeyboardKey.enter;
      case 'backspace':
        return LogicalKeyboardKey.backspace;
      case 'delete':
        return LogicalKeyboardKey.delete;
      case 'tab':
        return LogicalKeyboardKey.tab;
      case 'escape':
        return LogicalKeyboardKey.escape;
      case 'space':
        return LogicalKeyboardKey.space;
      case 'f1':
        return LogicalKeyboardKey.f1;
      case 'f2':
        return LogicalKeyboardKey.f2;
      case 'f5':
        return LogicalKeyboardKey.f5;
      case 'f6':
        return LogicalKeyboardKey.f6;
      default:
        throw Exception('Unknown key: $keyString');
    }
  }

  @override
  String toString() => 'KeyBind($commandId: $key${when != null ? " when $when" : ""})';

  /// Convert to JSON
  Map<String, dynamic> toJson() => _$KeyBindToJson(this);

  /// Create from JSON
  factory KeyBind.fromJson(Map<String, dynamic> json) => _$KeyBindFromJson(json);

  /// JSON serializer
  static final serializer = JsonSerializer<KeyBind>(
    toJson: (instance) => instance.toJson(),
    fromJson: (json) => KeyBind.fromJson(json),
  );

  static final logger = loggerFor(KeyBind, Level.INFO);
}
