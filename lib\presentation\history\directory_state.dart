import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:mobx/mobx.dart';

import '../../app/domain/path.dart';
import '../../app/util/observable_utils.dart';
import '../directory_view/domain/sort.dart';

part '.gen/directory_state.g.dart';

// FIXME: This is more like... ViewState.
@JsonSerializable()
class DirectoryState extends _DirectoryStateBase with _$DirectoryState {
  DirectoryState(
    super.dir, {
    super.focusedPath,
    super.focusedRowIndex,
    super.expandedDirectories,
    super.selectedPaths,
    super.sort,
  });

  DirectoryState copyWith({
    RawPath? dir,
    RawPath? focusedPath,
    int? focusedRowIndex,
    Iterable<RawPath>? expandedDirectories,
    Iterable<RawPath>? selectedPaths,
    Iterable<Sort>? sort,
  }) {
    return DirectoryState(
      dir ?? this.dir,
      focusedPath: focusedPath ?? this.focusedPath,
      focusedRowIndex: focusedRowIndex ?? this.focusedRowIndex,
      expandedDirectories: expandedDirectories ?? this.expandedDirectories,
      selectedPaths: selectedPaths ?? this.selectedPaths,
      sort: sort ?? this.sort,
    );
  }

  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'dir': dir.toJson(),
      if (_focusedPath != dir) 'focusedPath': _focusedPath.toJson(),
      if (_focusedRowIndex != 0) 'focusedRowIndex': _focusedRowIndex,
      if (expandedDirectories.isNotEmpty) 'expandedDirectories': expandedDirectories.map((e) => e.toJson()).toList(),
      if (selectedPaths.isNotEmpty) 'selectedPaths': selectedPaths.map((e) => e.toJson()).toList(),
      if (!listEquals(sort, defaultSort)) 'sort': sort.map((e) => e.toJson()).toList(),
    };
  }

  factory DirectoryState.fromJson(Map<String, dynamic> json) {
    return DirectoryState(
      // FIXME: Test what happens when dir is null.
      RawPath.fromJson(json['dir']),
      focusedPath: json['focusedPath'] == null ? null : RawPath.fromJson(json['focusedPath']),
      focusedRowIndex: json['focusedRowIndex'] as int? ?? 0,
      expandedDirectories: (json['expandedDirectories'] as List<dynamic>?)?.map((e) => RawPath.fromJson(e)).toList() ?? [],
      selectedPaths: (json['selectedPaths'] as List<dynamic>?)?.map((e) => RawPath.fromJson(e)).toList() ?? [],
      sort: (json['sort'] as List<dynamic>?)?.map((e) => Sort.fromJson(e as Map<String, dynamic>)).toList() ?? defaultSort,
    );
  }
}

@StoreConfig(hasToString: false)
abstract class _DirectoryStateBase with Store {
  final RawPath dir;

  @readonly
  RawPath _focusedPath;

  @readonly
  int _focusedRowIndex;

  // TODO: There's not much point in these observable lists, make it @observable instead.

  // This is always kept sorted.
  final expandedDirectories = ObservableList<RawPath>();

  final selectedPaths = ObservableSet<RawPath>();

  // TODO: It seems this would be more convenient as an immutable @observable.
  final sort = ObservableList<Sort>();

  _DirectoryStateBase(
    RawPath dir, {
    RawPath? focusedPath,
    int focusedRowIndex = 0,
    Iterable<RawPath>? expandedDirectories,
    Iterable<RawPath>? selectedPaths,
    Iterable<Sort>? sort,
  })  : dir = dir.resolved(),
        _focusedPath = focusedPath ?? dir,
        _focusedRowIndex = focusedRowIndex {
    if (expandedDirectories != null) {
      this.expandedDirectories
        ..addAll(expandedDirectories)
        ..sort();
    }
    if (selectedPaths != null) {
      this.selectedPaths.addAll(selectedPaths);
    }
    // TODO: Throw an error if sort is empty.
    this.sort.addAll((sort?.isNotEmpty ?? false) ? sort! : defaultSort);
  }

  @action
  void setFocusedPath(RawPath focusedPath, int focusedRowIndex) {
    _focusedPath = focusedPath;
    _focusedRowIndex = focusedRowIndex;
  }

  @action
  void addExpandedDirectory(RawPath path) {
    expandedDirectories.add(path);
    expandedDirectories.sort();
  }

  @action
  void removeExpandedDirectory(RawPath path) => expandedDirectories.remove(path);

  @action
  void clearExpandedDirectories() => expandedDirectories.clear();

  @action
  // FIXME: This will stop working because we're replacing the list with itself.
  void setSort(List<Sort> sort) => this.sort.replace(sort);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is _DirectoryStateBase &&
          runtimeType == other.runtimeType &&
          dir == other.dir &&
          _focusedPath == other._focusedPath &&
          _focusedRowIndex == other._focusedRowIndex &&
          listEquals(expandedDirectories, other.expandedDirectories) &&
          setEquals(selectedPaths, other.selectedPaths) &&
          listEquals(sort, other.sort);

  @override
  int get hashCode =>
      dir.hashCode ^ _focusedPath.hashCode ^ _focusedRowIndex.hashCode ^ expandedDirectories.hashCode ^ selectedPaths.hashCode ^ sort.hashCode;

  @override
  String toString() => "{dir=$dir, focusedRowIndex=$_focusedRowIndex, focusedPath=$_focusedPath}";
}

const defaultSort = [Sort.defaultSort];

// class DirectoryStateJsonConverter implements JsonConverter<DirectoryState, Map<String, dynamic>> {
//   const DirectoryStateJsonConverter();
//
//   @override
//   DirectoryState fromJson(Map<String, dynamic> json) => DirectoryState.fromJson(json);
//
//   @override
//   Map<String, dynamic> toJson(DirectoryState object) => <String, dynamic>{
//         'dir': object.dir.toJson(),
//         if (object._focusedPath != object.dir) 'focusedPath': object._focusedPath.toJson(),
//         if (object._focusedRowIndex != 0) 'focusedRowIndex': object._focusedRowIndex,
//         if (object.expandedDirectories.isNotEmpty)
//           'expandedDirectories': object.expandedDirectories.map((e) => e.toJson()).toList(),
//         if (object.selectedPaths.isNotEmpty)
//           'selectedPaths': object.selectedPaths.map((e) => e.toJson()).toList(),
//         if (!listEquals(object.sort, defaultSort))
//           'sort': object.sort.map((e) => e.toJson()).toList(),
//       };
// }
