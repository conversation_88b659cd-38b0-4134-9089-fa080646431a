import 'package:mek_data_class/mek_data_class.dart';

part '.gen/command_context.g.dart';

/// Represents the context in which a command is executed
@DataClass()
class CommandContext with _$CommandContext {
  const CommandContext({
    this.inFileList = false,
    this.inNavigationBarInput = false,
    this.inDialog = false,
    this.inContextMenu = false,
    this.inSearch = false,
    this.inCommandPalette = false,
    this.isRenaming = false,
  });

  static const defaultContext = CommandContext(
    inFileList: true,
    inDialog: false,
    inContextMenu: false,
    inSearch: false,
    inCommandPalette: false,
    isRenaming: false,
  );

  /// Whether the file list has focus
  final bool inFileList;

  /// Whether the navigation bar input has focus
  final bool inNavigationBarInput;

  /// Whether a dialog is open
  final bool inDialog;

  /// Whether a context menu is open
  final bool inContextMenu;

  /// Whether the search is active
  final bool inSearch;

  /// Whether the command palette is open
  final bool inCommandPalette;

  /// Whether a file is being renamed
  final bool isRenaming;

  @override
  String toString() => (ClassToString.flat('CommandContext')
        ..addIfExist('inFileList', _self.inFileList ? true : null)
        ..addIfExist('inDialog', _self.inDialog ? true : null)
        ..addIfExist('inContextMenu', _self.inContextMenu ? true : null)
        ..addIfExist('inSearch', _self.inSearch ? true : null)
        ..addIfExist('inCommandPalette', _self.inCommandPalette ? true : null)
        ..addIfExist('isRenaming', _self.isRenaming ? true : null))
      .toString();
}
