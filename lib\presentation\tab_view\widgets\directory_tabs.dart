import 'package:awesome_extensions/awesome_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:mobx/mobx.dart';
import 'package:provider/provider.dart';
import 'package:tabbed_view/tabbed_view.dart';

import '../../directory_view/directory_view_store.dart';
import '../../directory_view/widgets/file_list.dart';
import '../../directory_view/widgets/navigation_bar.dart' as app_nav;
import '../../history/history.dart';
import '../../util/context_extensions.dart';

class DirectoryTabs extends HookWidget {
  const DirectoryTabs({super.key, this.debugLabel});

  final String? debugLabel;

  @override
  Widget build(BuildContext context) {
    final historyStore = context.historyStore;

    // Create a TabbedViewController using the history data
    final tabController = useMemoized(() => TabbedViewController(
        historyStore.histories.map((e) => _createTabData(context, e, historyStore.histories.length)).toList(growable: true)));

    useEffect(() {
      return historyStore.histories.observe((change) {
        for (final change in change.elementChanges!) {
          switch (change.type) {
            case OperationType.add:
              tabController.addTab(_createTabData(context, change.newValue!, historyStore.histories.length));
              tabController.selectedIndex = historyStore.currentIndex;
              break;
            case OperationType.remove:
              if (change.index < tabController.tabs.length && tabController.getTabByIndex(change.index).value == change.oldValue) {
                tabController.removeTab(change.index);
              }
              break;
            case OperationType.update:
              // FIXME: Not sure what needs to happen here
              // tabController.updateTab(change.index,
              //     TabData(text: change.value.current.dir.absolutePath, content: fileList));
              break;
          }
        }
      });
    }, []);

    return TabbedViewTheme(
      data: TabbedViewThemeData.mobile(),
      child: TabbedView(
        controller: tabController,
        onTabClose: (index, _) => historyStore.delete(index),
        onTabSelection: (index) => historyStore.setCurrentIndex(index ?? 0),
        tabsAreaButtonsBuilder: (context, tabsCount) => [
          TabButton(
            icon: IconProvider.data(Icons.add),
            padding: const EdgeInsets.all(6),
            color: context.primaryColor,
            hoverColor: context.primaryColor,
            hoverBackground: BoxDecoration(color: context.theme.hoverColor),
            toolTip: "New tab",
            onPressed: historyStore.cloneCurrent,
          )
        ],
      ),
    );
  }

  TabData _createTabData(BuildContext context, History history, int numTabs) {
    // Get the existing stores from the context
    final paneStore = context.paneStore;
    final directoryViewStore = paneStore.directoryViewStore;

    // Create the tab content
    return TabData(
      text: history.current.dir.absolutePath,
      value: history,
      closable: numTabs > 1,
      content: Provider<DirectoryViewStore>.value(
        value: directoryViewStore,
        child: Column(
          children: [
            const app_nav.NavigationBar(),
            Expanded(child: FileList()),
          ],
        ),
      ),
    );
  }
}
