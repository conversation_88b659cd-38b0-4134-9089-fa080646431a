# History tests
- right side
- go back in history (how to trigger)
- go forward in history (how to trigger)
- switch tab

# Navigation tests
- start from non-existing dir
- navigate to non-existing dir
- navigate to non-existing dir which suddenly becomes existing
- navigate to existing dir which suddenly becomes non-existing
- navigate up from dir focuses on parent
- navigate into previously visited dir focuses on previously focused rows
- navigate back in history from dir focuses on last focused row
- navigate forward in history from dir focuses on last focused row
- navigate back in history from dir but previous row does not exist (fake scenario but still)
- navigate forward in history from a dir but that dir doesn't exist

# Focus tests
Renaming the last file in the list to something that bumps it higher (lets say to the first) makes the focused row disappear.

# Other tests
- Loading less than 50ms - no loading skeletons
- Loading more than 50ms - show loading skeletons
- No '..' on root.

# Navigation Bar tests
- Commands shouldn't react when navigation bar is focused.
- Relative dropdown should always start from current dir and show the current dir as the prefix followed by child matches.
- Navigation dropdown from C:\ starts at C:\\
- Arrow right on inline suggestion should select it, same as tab. Enter should enter it.
- Empty navigation dropdown (empty prefix) shows relative paths, but entering one of them throws an error.
- If there are no folders in current dir to offer, offer the current dir as the single suggestion.
- How to handle '/'?
- D:\d highlights the wrong bits of text.
- Any prefix that matches a directory exactly should be the first suggestion with a separator at the end.
- All suggestions should have a separator at the end.
- Going back to E:\qfiler throws an error and marks the wrong portion of the suggestion as a match. This is because the match is larger than the str length.
- The empty string throws an error.
- If prefix does not end with a separator and is a valid directory, first suggestion should be prefix with separator, then its children.
- Scrolling auto complete list where some of the folders are larger than 1 line (D:\Download) doesn't behave correctly.
- Selecting the auto complete suggestion E:\qfiler\test2\asd from E:\qfiler\test2\ causes [E:\qfiler\test2] Previously existed but now doesn't, reloading..
- Names starting with dots still don't work.
- Going to E:\qfiler, selecting .dart_tool as the suggestion and then typing \t causes a scroll animation.
- Going to D:\Download and typing wick instead of the text causes an exception.
- All suggestions must end with a separator.
- Unhandled Flutter Platform error: Not found: 'Ew\'
- empty prefix should show all mounted drives / start points.
- Clicking on the textfield should immediately add a separator to it.
- If all suggestions end with prefix, the first suggestion should not be automatically selected, because pressing enter will navigate to it.
- Alternatively, if suggestions don't end with a prefix, the first suggestion should be automatically selected.
- Make this a configuration value.
