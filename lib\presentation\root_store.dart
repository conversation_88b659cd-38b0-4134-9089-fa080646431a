import 'package:meta/meta.dart';
import 'package:mobx/mobx.dart';

import '../app/domain/path.dart';
import '../app/domain/user_choice.dart';
import '../app/file_system/file_repository.dart';
import '../app/file_system/file_system.dart';
import '../app/file_system/real/path_repository.dart';
import '../app/file_system/rust/rust_file_system_adapter.dart';
import '../app/notification/notification_repository.dart';
import '../app/notification/notification_store.dart';
import '../app/persistence/data_repository.dart';
import '../app/settings/settings_repository.dart';
import '../app/task/file_op_executor.dart';
import '../app/task/pending_file_store.dart';
import '../app/task/task.dart';
import '../app/task/task_planner.dart';
import '../app/task/task_store.dart';
import 'command/command_context_repository.dart';
import 'command/command_dispatcher.dart';
import 'command/command_repository.dart';
import 'command/keybind_manager.dart';
import 'command/keybind_repository.dart';
import 'directory_view/directory_view_store.dart';
import 'directory_view/navigation_bar_store.dart';
import 'domain/side.dart';
import 'history/history_store.dart';
import 'pane/all_panes_store.dart';
import 'pane/pane_store.dart';
import 'rename/rename_store.dart';

part '.gen/root_store.g.dart';

class RootStore extends RootStoreBase with _$RootStore {
  RootStore({
    required super.dataRepository,
    required super.fileRepository,
    required super.allPanesStore,
    required super.settingsRepository,
    required super.commandRepository,
    required super.keyBindRepository,
    required super.commandContextRepository,
    required super.keyBindManager,
    required super.commandDispatcher,
    required super.renameStore,
  });

  static Future<RootStore> create({
    RawPath? dataDir, // Used in tests
    HistoryStoreStateSnapshot? leftHistory, // Used in tests to set initial history
    HistoryStoreStateSnapshot? rightHistory, // Used in tests to set initial history
  }) async {
    final fileSystem = RustFileSystemAdapter();
    final pendingOperationRepository = PendingOperationRepository();
    final fileRepository = FileRepository(PathRepository(fileSystem), pendingOperationRepository);

    final notificationRepository = NotificationRepository();
    final notificationStore = NotificationStore();
    final executor = FileOpExecutor(
      fileSystem,
      pendingOperationRepository,
      _createShowConfirmOverwriteDialog(),
      _createShowFileOpErrorDialog(),
    );

    final jobPlanner = JobPlanner(fileRepository: fileRepository, pendingOperationRepository: pendingOperationRepository);

    final jobStore = JobStore(executor, notificationStore, jobPlanner);

    final dataRepository = createDataRepository(fileSystem, notificationRepository, dataDir);
    final settingsRepository = await SettingsRepository.create(dataRepository);
    final commandRepository = CommandRepository.create();
    final keyBindRepository = await KeyBindRepository.create(dataRepository);
    final commandContextRepository = CommandContextRepository();

    final renameStore = RenameStore(jobStore, notificationStore, commandContextRepository);
    // Set up the rename sync listener to synchronize rename operations across panes
    renameStore.setupRenameSyncListener();

    final left = await createPaneStore(
        settingsRepository, fileRepository, commandContextRepository, dataRepository, notificationStore, renameStore, Side.left,
        initialHistory: leftHistory);
    final right = await createPaneStore(
        settingsRepository, fileRepository, commandContextRepository, dataRepository, notificationStore, renameStore, Side.right,
        initialHistory: rightHistory);

    // Create AllPanesStore first
    final allPanesStore = AllPanesStore(left: left, right: right);

    // Create CommandDispatcher
    final commandDispatcher = CommandDispatcher(
      allPanesStore: allPanesStore,
      commandContextRepository: commandContextRepository,
      jobStore: jobStore,
      notificationStore: notificationStore,
    );

    // Create KeyBindManager with the repositories
    final keyBindManager = KeyBindManager(
      keyBindRepository,
      commandRepository,
      commandContextRepository,
      commandDispatcher,
    );

    // Set the AllPanesStore reference in the pane stores
    left.setAllPanesStore(allPanesStore);
    right.setAllPanesStore(allPanesStore);

    return RootStore(
      dataRepository: dataRepository,
      fileRepository: fileRepository,
      allPanesStore: allPanesStore,
      settingsRepository: settingsRepository,
      commandRepository: commandRepository,
      keyBindRepository: keyBindRepository,
      commandContextRepository: commandContextRepository,
      keyBindManager: keyBindManager,
      commandDispatcher: commandDispatcher,
      renameStore: renameStore,
    );
  }

  static DataRepository createDataRepository(FileSystem fileSystem, NotificationRepository notificationRepository, [RawPath? dataDir]) {
    final userDataDir = dataDir ?? RawPath.currentWorkingDir.child("data");
    return DataRepository(DataRepositoryConfig(userDataDir: userDataDir), fileSystem, notificationRepository);
  }

  static Future<PaneStore> createPaneStore(
    SettingsRepository settingsRepository,
    FileRepository fileRepository,
    CommandContextRepository commandContextRepository,
    DataRepository dataRepository,
    NotificationStore notificationStore,
    RenameStore renameStore,
    Side side, {
    HistoryStoreStateSnapshot? initialHistory,
  }) async {
    final historyStore = await HistoryStore.create(dataRepository, side, defaultHistory: initialHistory);
    final directoryViewStore = DirectoryViewStore(historyStore, fileRepository, commandContextRepository, renameStore, side.name);
    final navigationBarStore = NavigationBarStore(fileRepository: fileRepository, commandContextRepository: commandContextRepository);
    final paneStore = PaneStore(historyStore, directoryViewStore, navigationBarStore, side);
    return paneStore;
  }

  // Helper method to create ShowConfirmOverwriteDialog implementation
  static ShowConfirmOverwriteDialog _createShowConfirmOverwriteDialog() {
    return _SimpleShowConfirmOverwriteDialog();
  }

  // Helper method to create ShowFileOpErrorDialog implementation
  static ShowFileOpErrorDialog _createShowFileOpErrorDialog() {
    return _SimpleShowFileOpErrorDialog();
  }
}

// Simple implementation of ShowConfirmOverwriteDialog
class _SimpleShowConfirmOverwriteDialog implements ShowConfirmOverwriteDialog {
  @override
  Future<OverwriteChoice> call({required Operation op, required bool selectOverwriteAll}) async {
    // For now, always cancel
    return OverwriteChoice(overwriteMode: OverwriteMode.cancel, all: selectOverwriteAll);
  }
}

// Simple implementation of ShowFileOpErrorDialog
class _SimpleShowFileOpErrorDialog implements ShowFileOpErrorDialog {
  @override
  Future<OperationErrorChoice> call({required Operation op, required Object error}) async {
    // For now, always cancel
    return OperationErrorChoice(mode: OperationErrorMode.cancel, all: true);
  }
}

abstract class RootStoreBase with Store {
  final DataRepository dataRepository;
  final FileRepository fileRepository;
  final AllPanesStore allPanesStore;
  final SettingsRepository settingsRepository;

  final CommandRepository commandRepository;
  final KeyBindRepository keyBindRepository;
  final CommandContextRepository commandContextRepository;
  final KeyBindManager keyBindManager;
  final CommandDispatcher commandDispatcher;
  final RenameStore renameStore;

  const RootStoreBase({
    required this.dataRepository,
    required this.fileRepository,
    required this.allPanesStore,
    required this.settingsRepository,
    required this.commandRepository,
    required this.keyBindRepository,
    required this.commandContextRepository,
    required this.keyBindManager,
    required this.commandDispatcher,
    required this.renameStore,
  });

  @visibleForTesting
  void dispose() {
    dataRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    allPanesStore.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    settingsRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    commandRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    commandContextRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    keyBindManager.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    commandDispatcher.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    renameStore.dispose(); // ignore: invalid_use_of_visible_for_testing_member
  }
}
