import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/history/directory_state.dart';
import 'package:qfiler/presentation/history/history.dart';
import 'package:qfiler/presentation/history/history_store.dart';
import 'package:qfiler/presentation/util/setup.dart';

import 'test_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  // TODO: tests to add:
  // TODO: - right side
  // TODO: - go back in history (how to trigger)
  // TODO: - go forward in history (how to trigger)
  // TODO: - switch tab

  // TODO: Navigation tests to add:
  // TODO: - start from non-existing dir
  // TODO: - navigate to non-existing dir
  // TODO: - navigate to non-existing dir which suddenly becomes existing
  // TODO: - navigate to existing dir which suddenly becomes non-existing
  // TODO: - navigate up from dir focuses on parent
  // TODO: - navigate into previously visted dir focuses on previously focused rows
  // TODO: - navigate back in history from dir focuses on last focused row
  // TODO: - navigate forward in history from dir focuses on last focused row
  // TODO: - navigate back in history from dir but previous row does not exist (fake scenario but still)
  // TODO: - navigate forward in history from a dir but that dir doesn't exist

  // Create simple test file hierarchy
  final leftFiles = [
    TestFile(name: 'file1.txt'),
    TestFile(name: 'subdir', isDirectory: true, children: [
      TestFile(name: 'nested_file.txt'),
    ]),
  ];

  final rightFiles = [
    TestFile(name: 'file2.txt'),
    TestFile(name: 'subdir2', isDirectory: true, children: [
      TestFile(name: 'nested_file.txt'),
    ]),
  ];

  group('History Persistence Tests', () {
    // TODO: Do it for the right as well

    testWidgets('History should persist on focused row change', (tester) async {
      final test = await prepareTest(tester, name: 'history_persistence_focused_row_test', leftFiles: leftFiles, rightFiles: rightFiles);

      final initialLeftHistory = HistoryStoreStateSnapshot([History.single(test.leftDir)]);

      // Verify initial state
      expect(test.historyStore(Side.left).snapshot(), equals(initialLeftHistory), reason: 'Initial history is not as expected');

      // Navigate down 1 row
      await test.sendKeyAndWait(LogicalKeyboardKey.arrowDown);

      final updatedLeftHistory =
          HistoryStoreStateSnapshot([History.single(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1)]);

      // Verify updated history is as expected and was persisted
      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
      expect(await readHistoryFromFile(test, Side.left), equals(updatedLeftHistory), reason: 'Persisted history is not as expected');
    });

    testWidgets('History should persist on directory change', (tester) async {
      final test = await prepareTest(tester, name: 'history_persistence_directory_change_test', leftFiles: leftFiles, rightFiles: rightFiles);

      // Navigate into subdirectory: .. is at index 0, subdir is at index 1
      // So we need 1 arrow down keypress to reach subdir
      await test.sendKeyAndWait(LogicalKeyboardKey.arrowDown);
      await test.sendKeyAndWait(LogicalKeyboardKey.enter);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
          DirectoryState(test.leftDir.child('subdir'))
        ], currentIndex: 1),
      ]);

      // Verify updated history is as expected and was persisted
      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
      expect(await readHistoryFromFile(test, Side.left), equals(updatedLeftHistory), reason: 'Persisted history is not as expected');
    });
  });

  group('Navigation Tests', () {
    // FIXME: Need 3 versions of this test - where the parent dir's focused row matches, when it doesn't match, and when its not in history at all
    testWidgets('Navigate to parent focuses on dir we navigated from in parent (parent focused dir matches)', (tester) async {
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_test',
          leftFiles: leftFiles,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  DirectoryState(basePath, focusedPath: basePath.child('subdir'), focusedRowIndex: 1),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
        ], currentIndex: 2),
      ]);

      // Verify updated history is as expected and was persisted
      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
      expect(await readHistoryFromFile(test, Side.left), equals(updatedLeftHistory), reason: 'Persisted history is not as expected');
    });

    testWidgets('Navigate to parent focuses on dir we navigated from in parent when right side is set to parent already', (tester) async {
      // This test is significant because if the directory we navigate to is already watched, some reactions may not run due to bugs.
      // For example, the directory will not load again (because its already loaded) and that can prevent focus tracking from working.
      final test = await prepareTest(tester,
          name: 'navigate_to_parent_test',
          leftFiles: leftFiles,
          rightFiles: rightFiles,
          leftHistory: (basePath) => HistoryStoreStateSnapshot([
                History([
                  DirectoryState(basePath, focusedPath: basePath.child('subdir'), focusedRowIndex: 1),
                  DirectoryState(basePath.child('subdir')),
                ], currentIndex: 1),
              ]));

      // Change right side to point to the parent of left. This will make sure left navigation to parent does not trigger a load.
      test.historyStore(Side.right).current.replace(History.single(test.leftDir));
      await tester.pumpAndSettle();
      test.assertReady();
      expect(test.historyStore(Side.right).current, equals(History.single(test.leftDir)));

      // Navigate to parent.
      await test.sendKeyAndWait(LogicalKeyboardKey.backspace);

      final updatedLeftHistory = HistoryStoreStateSnapshot([
        History([
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
          DirectoryState(test.leftDir.child('subdir')),
          DirectoryState(test.leftDir, focusedPath: test.leftDir.child('subdir'), focusedRowIndex: 1),
        ], currentIndex: 2),
      ]);

      // Verify updated history is as expected and was persisted
      expect(test.historyStore(Side.left).snapshot(), equals(updatedLeftHistory), reason: 'Updated history is not as expected');
      expect(await readHistoryFromFile(test, Side.left), equals(updatedLeftHistory), reason: 'Persisted history is not as expected');
    });
  });
}

Future<HistoryStoreStateSnapshot> readHistoryFromFile(TestState test, Side side) =>
    HistoryStore.readSnapshot(test.rootStore.dataRepository, side);
