import 'dart:async';
import 'dart:math' as math;

import 'package:dartx/dartx.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:mobx/mobx.dart';

import '../../app/domain/file.dart';
import '../../app/domain/path.dart';
import '../../app/file_system/file_repository.dart';
import '../../app/task/task.dart';
import '../../app/util/async_value.dart';
import '../../app/util/log_utils.dart';
import '../../app/util/quick_score.dart';
import '../command/command_context.dart';
import '../command/command_context_repository.dart';
import '../history/directory_state.dart';
import '../history/history.dart';
import '../history/history_store.dart';
import '../rename/rename_store.dart';
import '../util/widgets/auto_complete_text_field.dart';
import './domain/display_file.dart';
import './domain/sort.dart';

part '.gen/directory_view_store.g.dart';

// FIXME: The presence of a multi-line suggestion in the auto-complete dropdown messes up the scrolling.
// FIXME: Going to a directory that exists but with lowercasing it name works, but the parent dir is shown with an error.
class DirectoryViewStore = DirectoryViewStoreBase with _$DirectoryViewStore;

abstract class DirectoryViewStoreBase extends AsyncValue<List<DisplayFile>> with Store {
  @observable
  Files _directory;

  @observable
  List<Files> _expandedDirectories = const [];

  final _files = ObservableMap<RawPath, DisplayFile>();

  /// Indicates that both this store and the widget rendering the files are ready.
  /// Will only be true after the files have been loaded and rendered.
  @readonly
  bool _isReady = false;

//   @readonly _focusedRowDelta = 0

  final selectedFiles = ObservableSet<FileDisplayFile>();

  // TODO: Not sure this should be an async value at all. We have loading and error in this class already.
  @observable
  AsyncValue<DisplayFile?> focusedFile = AsyncValueSnapshot(value: null);

  /// Tracks the destination path of a file being renamed that was previously focused
  RawPath? _pendingRenameFocusedFile;

  final HistoryStore _historyStore;
  final FileRepository _fileRepository;
  final CommandContextRepository _commandContextRepository;
  final WatchFileSession _directorySession;
  final WatchFileSession _directoryParentSession;
  final WatchFileSession _expandedDirectorySession;
  final WatchFileSession _autoCompleteSession;
  final RenameStore renameStore;

  final List<Dispose> _disposers = [];

  // Disposers specific to the current directory, cleared when directory changes
  final List<Dispose> _directoryDisposers = [];

  DirectoryViewStoreBase(this._historyStore, this._fileRepository, this._commandContextRepository, this.renameStore, String clientId)
      : _directorySession = _fileRepository.createWatchFileClient(clientId),
        _directoryParentSession = _fileRepository.createWatchFileClient("${clientId}_parent"),
        _expandedDirectorySession = _fileRepository.createWatchFileClient("${clientId}_expanded"),
        _autoCompleteSession = _fileRepository.createWatchFileClient("${clientId}_auto_complete}", maxWatchedDirs: 5),
        _directory = _fileRepository.emptyFiles {
    _disposers.addAll([
      _trackCurrentDir().call,
      _trackFiles().call,
      _trackFocusedFile().call,
      _trackExpandedDirectories().call,
      _directorySession.unwatchAll,
      _directoryParentSession.unwatchAll,
      _expandedDirectorySession.unwatchAll,
      _autoCompleteSession.unwatchAll,
    ]);
  }

  void _clearDirState() {
    for (final disposer in _directoryDisposers) {
      disposer();
    }
    _directoryDisposers.clear();

    _files.clear();
    _pendingRenameFocusedFile = null;
  }

  ReactionDisposer _trackCurrentDir() {
    var prevDir = RawPath.Null;
    return autorun((_) {
      final newDir = dir;
      if (newDir == prevDir) {
        return;
      }

      // Clear previous directory state
      _clearDirState();

      if (prevDir != RawPath.Null) {
        if (kDebugMode) {
          logger.info("$prevDir Directory changed -> $newDir");
        }
        _directorySession.unwatchDir(prevDir);
        final prevDirParent = prevDir.parent;
        if (prevDirParent != null) {
          _directoryParentSession.unwatchDir(prevDirParent);
        }
      }
      _directory = _directorySession.watchDir(newDir);
      _setFile(ParentDirDisplayFile(_directory.dir));

      // Set up tracking of pending operations for the new directory
      _directoryDisposers.add(_trackFileChanges().call);

      final newDirParent = newDir.parent;
      if (newDirParent != null) {
        _directoryParentSession.watchDir(newDirParent);
      }
      prevDir = newDir;
    }, name: "DirectoryViewStore.trackCurrentDir");
  }

  /// Sets up tracking of pending operations for the current directory
  Dispose _trackFileChanges() {
    return _directory.observeFiles((change) {
      switch (change.type) {
        case OperationType.add:
          final file = change.newValue!;
          if (kDebugMode) {
            logger.finest(
                "$dir _trackFileChanges() File added: $file${(file.pendingOperations?.isNotEmpty ?? false) ? ' with pending operations ${file.pendingOperations}' : ''}");
          }
          // if (_files.containsKey(file.path)) {
          //   logger.warning("$dir _trackFileChanges() File already exists in files: $file");
          // }
          assert(!_files.containsKey(file.path), "$dir _trackFileChanges() File already exists in files: $file");
          final displayFile = FileDisplayFile(file);
          _setFile(displayFile);

        case OperationType.update:
          final oldFile = change.oldValue!;
          final newFile = change.newValue!;
          if (kDebugMode) {
            final changes = oldFile.diffsForLog(newFile);
            assert(changes.isNotEmpty, "$dir _trackFileChanges($oldFile) No diffs detected against $newFile");
            logger.finest("$dir _trackFileChanges() File updated: $newFile ${changes.join(', ')}");
          }
          assert(newFile.path == oldFile.path, "$dir _trackFileChanges() File path changed on update: $oldFile -> $newFile");
          assert(_files.containsKey(newFile.path), "$dir _trackFileChanges() File not found in files: $newFile");

          _setFile(FileDisplayFile(newFile));

        case OperationType.remove:
          final file = change.oldValue!;
          if (kDebugMode) {
            logger.finest("$dir _trackFileChanges() File removed: $file");
          }

          if (file.path == focusedPath) {
            if (kDebugMode) {
              logger.finer("$dir _trackFileChanges() Focused file was removed: $file");
            }

            // When the focused file is removed, check if it's due to a rename (rename was the last op).
            // If it is, save it and refocus on it when it's added back.
            // Check if the file has pending operations, specifically a rename
            if (file.pendingOperations?.isEmpty ?? true) {
              if (kDebugMode) {
                logger.finer("$dir _trackFileChanges() Focused file was removed, but has no pending operations: $file");
              }
              return;
            }

            final op = file.pendingOperations!.last;
            if (op is! RenameOperation) {
              if (kDebugMode) {
                logger.finer("$dir _trackFileChanges() Focused file was removed, but last operation is not a rename: $file, $op");
              }
              return;
            }
            if (file.path != op.source.path) {
              if (kDebugMode) {
                logger.finer("$dir _trackFileChanges() Focused file was removed, but is not the source of the rename: $file ${op.source.path}");
              }
              return;
            }
            if (op.status != OperationStatus.completed) {
              if (kDebugMode) {
                logger.finer("$dir _trackFileChanges() Focused file was removed, but rename operation is not completed: $file $op");
              }
              return;
            }

            if (kDebugMode) {
              logger.finer("$dir _trackFileChanges() Focused file was removed, and is the source of a completed rename: $file $op");
            }
            _pendingRenameFocusedFile = op.destination;
          }

          _removeFile(file.path);

        case null:
          throw Exception('Unknown operation type');
      }
    }, fireImmediately: true);
  }

  ReactionDisposer _trackFiles() {
    return autorun((_) {
      if (isLoading || isLoadingExpandedDirectories) {
        _isReady = false;
        return;
      }

      // final List<DisplayFile> files = [];
      // final parentDir = dirDisplayFile;
      // if (parentDir != null) {
      //   _files[parentDir.file.path] = parentDir;
      //   // files.add(parentDir);
      // }

      // final dir = _directory;
      // // if (!this.error) {
      // files.addAll(dir.files.map(FileDisplayFile.new));
      // // }

      // expandedDirectories are naturally sorted from parent to child which should guarantee that
      // any expanded directory's FileDisplay will already be present in `files`
      // for (final dir in _directoryState.expandedDirectories) {
      // final expandedDirFiles = _expandedDirectoryWatcher.watchDir(dir) ;
      // if (!expandedDirFiles.isLoading) {
      // final dirFile = files.firstWhere((it) => it.file.hasPath(dir));
      // if (dirFile != null) {
      // if (expandedDirFiles.error == null) {
      //   final nestingLevel = calcNestingLevel(dirFile) + 1 ;
      //   for (final it in expandedDirFiles.files) {
      //     files.add({ ...it, parent: dirFile, nestingLevel })
      //   }
      // } else if (FileDisplay.isReal(dirFile)) {
      // dirFile.error = content.error;
      // }
      // } else {
      // this.log.warn(`Expanded dir not found in files: ${dir}`);
      // this._directoryState.removeExpandedDirectory(dir);
      // }
      // }
      // }

      // _setFiles(files..sort(_fileComparator));
    }, name: "DirectoryViewStore.trackFiles");
  }

  @computed
  List<DisplayFile> get files => _files.values.sortedWith(_fileComparator).toList();

  // @action
  // void _setFiles(List<DisplayFile> files) {
  //   this.files.replace(files);
  //   _files = files.map((it) => MapEntry(it.file.path, it)).toMap();
  // }

  @action
  void _setFile(DisplayFile file) {
    _files[file.file.path] = file;
  }

  @action
  void _removeFile(RawPath path) {
    _files.remove(path);
  }

  @action
  void setIsReady(bool ready) {
    if (ready != _isReady) {
      _isReady = ready;
    }
  }

  ReactionDisposer _trackExpandedDirectories() {
    return autorun((_) {
      _expandedDirectories = _directoryState.expandedDirectories.map((dir) => _expandedDirectorySession.watchDir(dir)).toList();
    });
  }

  ReactionDisposer _trackFocusedFile() {
    return autorun((_) {
      final files = this.files;
      final focusedPath = this.focusedPath;
      final focusedRowIndex = this.focusedRowIndex;
      logger.finest("$dir _trackFocusedFile() Running with focusedPath=$focusedPath, focusedRowIndex=$focusedRowIndex");

      // Check loading and error states
      if (isLoading || isLoadingExpandedDirectories) {
        logger.finest("$dir _trackFocusedFile() isLoading=$isLoading, isLoadingExpandedDirectories=$isLoadingExpandedDirectories");
        this.focusedFile = AsyncValueSnapshot(value: null, isLoading: true);
        return;
      }

      if (error != null && files.isEmpty) {
        logger.finest("$dir _trackFocusedFile() error=$error, files.isEmpty=${files.isEmpty}");
        this.focusedFile = AsyncValueSnapshot(value: null, error: error);
        return;
      }

      // This autorun can run if any of its tracked observables changes.
      // Specifically, it can happen that isLoading changes to false, but the autorun that updates "files" did not run yet.
      // This will mean that this autorun is running with the new focusedRowIndex and focusedPath, but with the old files list.
      if (!_isReady) {
        logger.finest("$dir _trackFocusedFile() _isReady=false");
        this.focusedFile = AsyncValueSnapshot(value: null);
        return;
      }
      logger.finest("$dir _trackFocusedFile() _isReady=true");

      // Check if the focused row index is valid
      if (focusedRowIndex < 0 || focusedRowIndex >= files.length) {
        logger.finest("$dir _trackFocusedFile() focusedRowIndex is invalid: focusedRowIndex=$focusedRowIndex, files.length=${files.length}");
        this.focusedFile = AsyncValueSnapshot(value: null);
        return;
      }

      // Get the file at the focused row
      final focusedFile = files[focusedRowIndex];

      // Check if the file at the focused row matches the focused path
      if (focusedFile.file.path == focusedPath) {
        logger.finest("$dir _trackFocusedFile() focusedFile matches focusedPath: ${focusedFile.file.path} == $focusedPath");
        this.focusedFile = AsyncValueSnapshot(value: focusedFile);
        return;
      }

      // The current focus is not on the desired file. This is probably a result of a "shift" in the file system.
      // Try to find the file with the focused path
      final result = _findFocusedFile(focusedPath, this.focusedFile);

      if (kDebugMode) {
        logger.info("$dir Adjusting focus: [$focusedRowIndex]$focusedPath -> [${result.index}]${result.file}: ${result.reason}");
      }

      // Update the focused file
      _directoryState.setFocusedPath(result.file, result.index);

      // Update the focused file value
      if (result.exactMatch) {
        final newFocusedFile = files[result.index];
        this.focusedFile = AsyncValueSnapshot(value: newFocusedFile);
      } else {
        this.focusedFile = AsyncValueSnapshot(value: null);
      }
    });
  }

  FindFileByPathResult _findFocusedFile(RawPath targetPath, AsyncValue<DisplayFile?> prevFocus) {
    final filesList = files;

    // If target is the current directory, return the parent directory entry
    if (dir == targetPath) {
      logger.finest("$dir _findFocusedFile($targetPath) target is the current directory");
      final parentDir = dirDisplayFile;
      final exactMatch = parentDir != null;
      if (exactMatch) {
        return FindFileByPathResult(
          file: parentDir.file.path,
          index: 0,
          reason: "Focus on parent directory",
          exactMatch: true,
        );
      } else {
        return FindFileByPathResult(
          file: filesList[0].file.path,
          index: 0,
          exactMatch: false,
          reason: "Current directory has no parent dir!",
        );
      }
    }

    // Try to find the target file in the current files list
    final index = filesList.indexWhere((file) => file.file.path == targetPath);
    if (index != -1) {
      logger.finest("$dir _findFocusedFile($targetPath) target found in a different index");
      return FindFileByPathResult(
        file: filesList[index].file.path,
        index: index,
        exactMatch: true,
        reason: "Target found in a different index",
      );
    }

    // Check if we have a pending rename destination that we're tracking
    if (_pendingRenameFocusedFile != null) {
      // Check if the target path is the source of a rename operation
      final destIndex = filesList.indexWhere((file) => file.file.path == _pendingRenameFocusedFile);
      if (destIndex != -1) {
        if (kDebugMode) {
          logger.info("$dir _findFocusedFile($targetPath) Found pending rename destination: $_pendingRenameFocusedFile");
        }

        // Clear the pending rename destination since we found it
        _pendingRenameFocusedFile = null;

        return FindFileByPathResult(
          file: filesList[destIndex].file.path,
          index: destIndex,
          exactMatch: true,
          reason: "Found renamed file destination",
        );
      }
    }

    // Apply fallback strategy if target not found
    // FIXME: prevFocus can never be isLoading == true here
    final fallback = !prevFocus.isLoading ? Fallback.closestMatch : Fallback.currentIndex;
    logger.finest("$dir _findFocusedFile($targetPath) focused file not found, applying fallback=$fallback");

    switch (fallback) {
      case Fallback.closestMatch:
        // Try to find the closest match
        final closestIndex = _findClosestMatchIndex(targetPath, filesList);
        return FindFileByPathResult(
          file: filesList[closestIndex].file.path,
          index: closestIndex,
          exactMatch: false,
          reason: "Target not found, fallback to closest match",
        );

      case Fallback.currentIndex:
        // Fallback to current index
        final index = math.min(math.max(0, focusedRowIndex), filesList.length - 1);
        logger.finest("$dir _findFocusedFile($targetPath) fallback to current index: [$index]${filesList[index].file.path}");
        return FindFileByPathResult(
          file: filesList[index].file.path,
          index: index,
          exactMatch: false,
          reason: "Target not found, fallback to current index",
        );
    }
  }

  int _findClosestMatchIndex(RawPath targetPath, List<DisplayFile> files) {
    // In the legacy code, this uses a binary search with a comparator
    // For simplicity, we'll use a linear search for now
    // This could be optimized later if needed

    // Default to the first file if no match is found
    if (files.isEmpty) return 0;

    // Try to find a file with a similar name
    final targetName = targetPath.name;
    final index = files.indexWhere((file) => file.file.path.name.toLowerCase().contains(targetName.toLowerCase()));

    if (index != -1) {
      logger.finest("$dir _findClosestMatchIndex($targetPath) found similar name: [$index]${files[index].file.path}");
      return index;
    }

    // If no similar name is found, return the current focused row index or 0
    logger.finest(
        "$dir _findClosestMatchIndex($targetPath) no similar name found, returning current focused row index or 0: [$focusedRowIndex]${files[focusedRowIndex].file.path}");
    return math.min(math.max(0, focusedRowIndex), files.length - 1);
  }

  @override
  bool get isLoading => _directory.isLoading;

  @override
  Exception? get error => _directory.error;

  // FIXME: This is incorrect
  @override
  Future<List<DisplayFile>> get future => Future.value(files);

  @override
  List<DisplayFile> get value => files;

  History get _history => _historyStore.current;

  DirectoryState get _directoryState => _history.current;

  RawPath get dir => _directoryState.dir;

  @computed
  ParentDirDisplayFile? get dirDisplayFile {
    final dir = _directory.dir;
    return !dir.isRoot ? ParentDirDisplayFile(dir) : null;
  }

  ObservableList<Sort> get sort => _directoryState.sort;

  // TODO: It might be nicer to not separate these from _files, consider all of them as expanded directories.
  // @computed
  // List<Files> get _expandedDirectories => _directoryState.expandedDirectories
  //     .map(_expandedDirectoryHandler.watchDir)
  //     .toList();

  // TODO: This is not how we want to do things. We only want to load the topmost directory, and then
  // TODO: load the expanded directories after already rendering the topmost dir, with spinners next
  // TODO: to the directories that are loading.
  @computed
  bool get isLoadingExpandedDirectories => _expandedDirectories.any((it) => it.isLoading);

  RawPath get focusedPath => _directoryState.focusedPath;

  int get focusedRowIndex => _isReady ? _directoryState.focusedRowIndex : -1;

  @action
  bool setFocusedFile(DisplayFile displayFile) {
    final index = files.indexOf(displayFile);
    if (index == -1) {
      if (kDebugMode) {
        logger.warning("$dir setFocusedFile($displayFile) Cannot focus on file not in files list!");
      }
      return false;
    }
    _directoryState.setFocusedPath(displayFile.file.path, index);
    return true;
  }

  @action
  void executeFocusedFile() {
    final file = focusedFile.value;
    if (file != null) {
      executeFile(file);
    } else {
      if (kDebugMode) {
        logger.warning("$dir executeFocusedFile() Cannot execute null focused file!");
      }
    }
  }

  /// Start renaming the focused file
  @action
  void startRenameFocusedFile() {
    final file = focusedFile.value;
    if (file != null) {
      renameStore.startRename(file);
    } else {
      if (kDebugMode) {
        logger.warning("$dir startRenameFocusedFile() Cannot rename null focused file!");
      }
    }
  }

  void executeFile(DisplayFile displayFile) {
    assert(_files.containsKey(displayFile.file.path), "$dir executeFile($displayFile): File not found in dir files!");
    final file = displayFile.file;

    // If this is a parent directory entry (..), navigate up
    if (displayFile is ParentDirDisplayFile) {
      goToParentDir();
    } else if (file.isDirectory) {
      changeDir(file.path);
    } else {
      // TODO: do this
      // this.fileExecutor.execute(file).catch(this.notificationStore.notifyError)
    }
  }

  @action
  void goToParentDir() {
    final parent = dir.parent;
    if (parent != null) {
      changeDir(parent);
    } else {
      if (kDebugMode) {
        logger.warning("$dir Cannot go up from the root!");
      }
    }
  }

  @action
  void changeRoot(RawPath newRoot) {
    if (newRoot.root == dir.root) {
      if (kDebugMode) {
        logger.fine("$dir changeRoot($newRoot) Root is already set to $newRoot, ignoring");
      }
      return;
    }

    // When switching roots, go to the last directory we visited in the new root, if any.
    final newDir = _history.findLast((it) => it.dir.root == newRoot.root)?.dir ?? newRoot;
    changeDir(newDir);
  }

  @action
  void changeDir(RawPath newDir) {
    final currentDir = dir;
    if (currentDir == newDir) {
      if (kDebugMode) {
        logger.info("$dir changeDir($newDir): Already set as current dir, ignoring.");
      }
      return;
    }
    if (kDebugMode) {
      logger.info("$dir changeDir($newDir)");
    }

    // This will prevent reactions that require everything to be loaded from running.
    // It will be set to true by a reaction when everything was loaded.
    _isReady = false;

    final history = _history;
    // TODO: Maybe consider calling `findNextEntry` instead?
    if (newDir == history.next?.dir) {
      goForwardInHistory();
      return;
    }

    RawPath pathToFocusOn;
    int rowIndexToFocusOn;
    String reason;
    List<RawPath>? expandedDirectories;
    Set<RawPath>? selectedPaths;
    final prevVisited = history.findPrev((it) => it.dir == newDir);
    if (prevVisited != null) {
      // We've visited newDir previously, re-focus on the last focused file from history.
      // However, avoid registering this autorun to any changes in the history entry that we're reading from.
      pathToFocusOn = prevVisited.focusedPath;
      rowIndexToFocusOn = prevVisited.focusedRowIndex;
      expandedDirectories = prevVisited.expandedDirectories;
      selectedPaths = prevVisited.selectedPaths;
      reason = "Previously visited directory";
    } else if (newDir.isParentOf(currentDir)) {
      // We just navigated up from currentDir, re-select currentDir in the newDir files.
      pathToFocusOn = currentDir;
      rowIndexToFocusOn = -1;
      reason = "Navigate to parent directory";
    } else {
      pathToFocusOn = newDir;
      rowIndexToFocusOn = 0;
      reason = "Newly encountered directory";
    }

    // TODO: Only push valid paths here?
    // If the rowIndex is invalid or points to another file, it will be taken care of by the reaction that monitors this.
    history.push(DirectoryState(newDir,
        focusedPath: pathToFocusOn, focusedRowIndex: rowIndexToFocusOn, expandedDirectories: expandedDirectories, selectedPaths: selectedPaths));

    if (kDebugMode) {
      logger.fine("$dir changeDir($newDir) focus=[$rowIndexToFocusOn]$pathToFocusOn: $reason");
    }
  }

  void goBackInHistory() {
    if (kDebugMode) {
      logger.info("$dir goBackInHistory(): ${_history.prev}");
    }
    _history.goBack();
  }

  void goForwardInHistory() {
    if (kDebugMode) {
      logger.info("$dir goForwardInHistory(): ${_history.next}");
    }
    _history.goForward();
  }

  Future<void> reload() => _directory.reload();

  @action
  void setSort(List<Sort> sort) {
    // Always set the sort, even if it's empty, because that will run a reaction that will update
    // the UI to display the correct sort.
    _directoryState.setSort(sort.isNotEmpty ? sort : this.sort);
  }

  @computed
  Comparator<DisplayFile> get _fileComparator {
    final sort = this.sort;
    return (a, b) {
      if (a is ParentDirDisplayFile) return -1;
      if (b is ParentDirDisplayFile) return 1;

      for (final columnSort in sort) {
        final file1 = a.file;
        // final file1NestingLevel = a.nestingLevel ?? 0;
        // final file1Parent = a.parent;

        final file2 = b.file;
        // final file2NestingLevel = b.nestingLevel ?? 0;
        // final file2Parent = b.parent;

        // // First, bring both files to the same nesting level.
        // if (file1NestingLevel < file2NestingLevel) {
        //   return comparator(f1, file2Parent!);
        // }
        // if (file1NestingLevel > file2NestingLevel) {
        //   return comparator(file1Parent!, f2);
        // }

        // // At this point both file1 & file2 are at the same nesting level.
        // // If they have the same parent - they should be sorted by the natural order in the parent.
        // // If they have different parents - they should be sorted according to their parents.
        // if (file1Parent && file2Parent && !file1Parent.file.equals(file2Parent.file)) {
        //   return comparator(file1Parent, file2Parent);
        // }

        // At this point, both file1 & file2 either have no parent or have the same parent.
        if (columnSort.directoryFirst) {
          if (file1.isDirectory && !file2.isDirectory) {
            return -1;
          }
          if (!file1.isDirectory && file2.isDirectory) {
            return 1;
          }
        }

        final result = columnSort.compare(a, b);
        if (result != 0) {
          return result;
        }
      }

      return 0;
    };
  }

  AsyncValue<AutoCompleteSuggestions<File>> calcAutoCompleteSuggestions(String prefix) {
    // FIXME: How to handle '/'?
    // FIXME: E:\qfi highlights the wrong bits of text.
    // FIXME: Any prefix that matches a directory exactly should be the first suggestion with a separator at the end.
    // FIXME: All suggestions should have a separator at the end.
    // FIXME: Going back to E:\qfiler throws an error and marks the wrong portion of the suggestion as a match. This is because the match is larger than the str length.
    // FIXME: The empty string throws an error.
    // FIXME: If prefix does not end with a separator and is a valid directory, first suggestion should be prefix with separator, then its children.
    // FIXME: Scrolling auto complete list where some of the folders are larger than 1 line (D:\Download) doesn't behave correctly.
    // FIXME: Selecting the auto complete suggestion E:\qfiler\test2\asd from E:\qfiler\test2\ causes [E:\qfiler\test2] Previously existed but now doesn't, reloading..
    // FIXME: Names starting with dots still don't work.
    // FIXME: Going to E:\qfiler, selecting .dart_tool as the suggestion and then typing \t causes a scroll animation.
    // FIXME: Going to D:\Download and typing wick instead of the text causes an exception.
    // FIXME: All suggestions must end with a separator.
    // FIXME: Unhandled Flutter Platform error: Not found: 'Ew\'
    // FIXME: empty prefix should show all mounted drives / start points.
    // FIXME: Clicking on the textfield should immediately add a separator to it.
    // FIXME: If all suggestions end with prefix, the first suggestion should not be automatically selected, because pressing enter will navigate to it.
    // FIXME: Alternatively, if suggestions don't end with a prefix, the first suggestion should be automatically selected.
    // FIXME: Make this a configuration value.

    // TODO: Is the below needed?
    // if (prefix.isEmpty) {
    //   // Return current dir with all children marked as matches.
    //   return _directory.snapshot.map((files) => AutoCompleteSuggestions(
    //       files.filter((e) => e.isDirectory).toList(),
    //       matches: files.map((e) => [TextRange(start: 0, end: e.absolutePath.length)]).toList()));
    // }

    final unresolvedPrefix = prefix.asPath(resolve: false);
    final resolvedPrefix = prefix.isNotEmpty ? unresolvedPrefix.resolved() : dir;
    final parent = resolvedPrefix.parent;

    final RawPath dirToFetch;
    final String? nameToMatch;
    if (prefix.isEmpty || prefix.endsWith(RawPath.pathSeparator) || parent == null) {
      // Fetch the directory that the resolvedPrefix points to and mark all its children as matches.
      dirToFetch = resolvedPrefix;
      nameToMatch = null;
    } else {
      dirToFetch = parent;
      nameToMatch = resolvedPrefix.name;
    }

    if (kDebugMode) {
      logger.finest(
          "$dir calcAutoCompleteSuggestions($prefix): dirToFetch=$dirToFetch, nameToMatch=$nameToMatch, unresolvedPrefix=$unresolvedPrefix, resolvedPrefix=$resolvedPrefix");
    }

    // Add matches of the prefix from the parent.
    // _autoCompleteHandler.watchDir is safe to call multiple times because result of watching is
    // cached and only performed once. On top of that, it is limited to watch at most 5 directories
    // after which it starts un-watching the oldest ones.
    final currentDirFiles = _autoCompleteSession.watchDir(dirToFetch);
    if (!currentDirFiles.hasValue) {
      // Return loading or error.
      return _autoCompleteResultWithoutMatches(currentDirFiles, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix);
    }

    // Check which directories in the current dir match the prefix.
    final List<File> candidates = currentDirFiles.files.filter((e) => e.isDirectory).toList();

    final quickScore = QuickScore<File>(candidates, {"name": (e) => e.name}, sortTieBreaker: File.naturalComparator);
    final results = quickScore.search(nameToMatch ?? "");

    // If prefix matches the name of child directory exactly, add it as the first suggestion and
    // add its children below it in the results.
    final fileInParent = nameToMatch != null ? currentDirFiles.get(nameToMatch) : null;
    if (fileInParent != null && fileInParent.isDirectory) {
      // We have an exact match.
      final childFiles = _autoCompleteSession.watchDir(resolvedPrefix);
      if (!childFiles.hasValue) {
        // return childFiles;
        return _autoCompleteResultWithoutMatches(childFiles, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix);
      }

      int indexOfFileInResults = results.indexWhere((e) => e.item == fileInParent);
      // This should never happen, but if the index of our exact match is not 0, make it 0.
      // Exact matches should always be first.
      if (indexOfFileInResults != 0) {
        if (indexOfFileInResults == -1) {
          // This should definitely never happen.
          assert(
              false,
              "Prefix matches a child exactly but is not in results: prefix=$prefix, "
              "nameToMatch=$nameToMatch, fileInParent=$fileInParent, results=$results");
          results.insert(0, QuickScoreResult<File>(fileInParent, key: "name", score: 1, matches: [Match(start: 0, end: nameToMatch!.length)]));
        } else {
          final result = results.removeAt(indexOfFileInResults);
          results.insert(0, result);
        }
      }

      // If our exact match doesn't end with a path separator, add one.
      // FIXME: Later we add a separator to all suggestions anyway.
      // final path = results[0].item.path;
      // if (!path.endsWith(Path.pathSeparator)) {
      //   results[0] =
      //       results[0].copyWith(item: results[0].item.withPath(path.append(Path.pathSeparator)));
      // }

      // Add the children of the exact match directly below it.
      results.insertAll(
          1,
          childFiles.files.filter((e) => e.isDirectory).map((e) => QuickScoreResult<File>(e,
              key: "name", score: 1, matches: [Match(start: 0, end: nameToMatch!.length + RawPath.pathSeparator.length + e.name.length)])));
    }

    // We only match the last element of the prefix, so we must adjust the match ranges.
    final prefixLength = nameToMatch == null ? unresolvedPrefix.absolutePath.length : unresolvedPrefix.dir.length + RawPath.pathSeparator.length;

    return AsyncValueSnapshot(
        value: AutoCompleteSuggestions(results.map((e) {
      return SuggestionItem(_replacePrefixOfSuggestion(e.item, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix),
          matches: e.matches.map((match) {
            return TextRange(start: match.start + prefixLength, end: match.end + prefixLength);
          }).toList());
    }).toList()));
  }

  AsyncValue<AutoCompleteSuggestions<File>> _autoCompleteResultWithoutMatches(AsyncValue<List<File>> files,
          {required RawPath unresolvedPrefix, required RawPath resolvedPrefix}) =>
      files.snapshot.map((value) {
        return AutoCompleteSuggestions(value
            .filter((e) => e.isDirectory)
            .map((e) => SuggestionItem(_replacePrefixOfSuggestion(e, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix)))
            .toList());
      });

  // Replaces the path of the suggestion except the last element with the unresolved prefix.
  // This is needed because the path resolver will remove special characters like '..' from the
  // prefix, but we want to preserve them and only auto-complete the last path elements.
  File _replacePrefixOfSuggestion(File suggestion, {required RawPath unresolvedPrefix, required RawPath resolvedPrefix}) {
    assert(suggestion.isDirectory, "Suggestions must only contain directories: $suggestion");
    // assert(resolvedPrefix.isAncestorOf(e.path),
    //     "Suggestions must only contain children of the prefix: $e, prefix=$resolvedPrefix");
    if (resolvedPrefix.isParentOf(suggestion.path)) {
      // The prefix points exactly to a directory and 'e' is a child of that directory.
      return suggestion.withPath(unresolvedPrefix.child(suggestion.name + RawPath.pathSeparator));
    }

    // if (e.elementNames.length == resolvedPrefix.elementNames.length) {
    final parent = unresolvedPrefix.parent;
    if (kDebugMode) {
      logger.finest(
          "$dir _replacePrefixOfSuggestions(suggestion=$suggestion, unresolvedPrefix=$unresolvedPrefix, resolvedPrefix=$resolvedPrefix): ${parent != null ? suggestion.withPath(parent.child(suggestion.name + RawPath.pathSeparator)) : suggestion}");
    }
    // The prefix does not point exactly to a directory.
    // Replace everything in 'e' except the last element with the unresolved prefix.
    // If the prefix ends with a separator, we must add it to the suggestion.
    final child = parent?.child(suggestion.name + RawPath.pathSeparator);
    if (kDebugMode) {
      logger.finest(
          "$dir _replacePrefixOfSuggestions(suggestion=$suggestion, unresolvedPrefix=$unresolvedPrefix, resolvedPrefix=$resolvedPrefix): child=$child, parent=$parent, e.name=${suggestion.name}");
    }
    return parent != null
        ? suggestion.withPath(child!)
        // parent.child(e.name).append(e.endsWith(Path.pathSeparator) ? Path.pathSeparator : ''))
        : suggestion;
    // }

    // suffix = endsWithSeparator
    //     ? e.name
    //     // Special handling is needed when auto completing a name beginning from dot, because
    //     // it will be removed by the path resolver.
    //     : prefixPathUnresolved.name == '.'
    //         ? e.name.startsWith('.')
    //             ? e.name.substring(1)
    //             : null
    //         : Path.pathSeparator + e.name;
    // return e.withPath(unresolvedPrefix.append(suffix));

    // final suggestions = result.suggestions.map((e) {
    //   final String? suffix;
    //   if (e.elementNames.length == unresolvedPrefix.elementNames.length) {
    //     // e is a direct child of prefix and we are auto-completing the last path element.
    //     suffix = e.name;
    //   } else {
    //     suffix = unresolvedPrefix.absolutePath.endsWith(Path.pathSeparator)
    //         ? e.name
    //         // Special handling is needed when auto completing a name beginning from dot, because
    //         // it will be removed by the path resolver.
    //         : unresolvedPrefix.name == '.'
    //             ? e.name.startsWith('.')
    //                 ? e.name.substring(1)
    //                 : null
    //             : Path.pathSeparator + e.name;
    //   }
    //   return suffix != null ? e.withPath(unresolvedPrefix.append(suffix)) : null;
    // }).toList();
    //
    // return CalcSuggestionsResult(suggestions, matches: result.matches);
  }

  void startAutoComplete() {
    _commandContextRepository.pushContext(const CommandContext(inNavigationBarInput: true));
  }

  void stopAutoComplete() {
    _autoCompleteSession.unwatchAll();
    _commandContextRepository.popContext(const CommandContext(inNavigationBarInput: true));
  }

  @action
  void moveFocusUp([int amount = 1]) {
    final prevIndex = focusedRowIndex;
    final newFocusedRowIndex = math.max(0, prevIndex - amount);
    final newFocusedPath = files[newFocusedRowIndex].file.path;
    _directoryState.setFocusedPath(newFocusedPath, newFocusedRowIndex);
  }

  @action
  void moveFocusDown([int amount = 1]) {
    final prevIndex = focusedRowIndex;
    final newFocusedRowIndex = math.min(prevIndex + amount, files.length - 1);
    final newFocusedPath = files[newFocusedRowIndex].file.path;
    _directoryState.setFocusedPath(newFocusedPath, newFocusedRowIndex);
  }

  @visibleForTesting
  void dispose() {
    for (final disposer in _disposers) {
      disposer();
    }
    _disposers.clear();
    // Unwatching all watched dirs is taken care of by disposers above
    _expandedDirectories.clear();
  }

  static final logger = loggerFor(DirectoryViewStore);
}

/// Fallback enum for file path resolution
enum Fallback {
  closestMatch,
  currentIndex,
}

/// NewFocusBehavior class for tracking focus behavior
class NewFocusBehavior {
  final RawPath focusOn;
  final String action;
  final Fallback fallback;

  const NewFocusBehavior({
    required this.focusOn,
    required this.action,
    required this.fallback,
  });
}

/// FindFileByPathResult class for file path resolution results
class FindFileByPathResult {
  final RawPath file;
  final int index;
  final String reason;
  final bool exactMatch;

  const FindFileByPathResult({
    required this.file,
    required this.index,
    required this.reason,
    required this.exactMatch,
  });
}

final caseSensitiveFilePathComparator = createDisplayFileComparator(File.naturalComparator);

Comparator<DisplayFile> createDisplayFileComparator(int Function(File, File) compare) {
  int comparator(DisplayFile f1, DisplayFile f2) {
    if (f1 is ParentDirDisplayFile) return -1;
    if (f2 is ParentDirDisplayFile) return 1;

    final file1 = f1.file;
    // final file1NestingLevel = f1.nestingLevel ?? 0;
    // final file1Parent = f1.parent;

    final file2 = f2.file;
    // final file2NestingLevel = f2.nestingLevel ?? 0;
    // final file2Parent = f2.parent;

    // // First, bring both files to the same nesting level.
    // if (file1NestingLevel < file2NestingLevel) {
    //   return comparator(f1, file2Parent!);
    // }
    // if (file1NestingLevel > file2NestingLevel) {
    //   return comparator(file1Parent!, f2);
    // }

    // // At this point both file1 & file2 are at the same nesting level.
    // // If they have the same parent - they should be sorted by the natural order in the parent.
    // // If they have different parents - they should be sorted according to their parents.
    // if (file1Parent && file2Parent && !file1Parent.file.equals(file2Parent.file)) {
    //   return comparator(file1Parent, file2Parent);
    // }

    // At this point, both file1 & file2 either have no parent or have the same parent.
    if (file1.isDirectory && !file2.isDirectory) {
      return -1;
    }
    if (!file1.isDirectory && file2.isDirectory) {
      return 1;
    }

    return compare(file1, file2);
  }

  return comparator;
}
