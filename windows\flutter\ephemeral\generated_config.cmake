# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\fvm\\versions\\3.22.3" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\dev\\qfiler" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.22.3"
  "PROJECT_DIR=D:\\dev\\qfiler"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.22.3"
  "FLUTTER_EPHEMERAL_DIR=D:\\dev\\qfiler\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\dev\\qfiler"
  "FLUTTER_TARGET=C:\\Users\\<USER>\\AppData\\Local\\Temp\\flutter_tools.4d1759f0\\flutter_test_listener.a8d53701/listener.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuNA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049NmZiYTI0NDdlOQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049OGNkMTllNTA5ZA==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE=,SU5URUdSQVRJT05fVEVTVF9TSE9VTERfUkVQT1JUX1JFU1VMVFNfVE9fTkFUSVZFPWZhbHNl"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\dev\\qfiler\\.dart_tool\\package_config.json"
)
