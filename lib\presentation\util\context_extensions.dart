import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../app/settings/settings_repository.dart';
import '../command/command_context_repository.dart';
import '../command/command_dispatcher.dart';
import '../command/command_repository.dart';
import '../command/keybind_manager.dart';
import '../directory_view/directory_view_store.dart';
import '../history/history_store.dart';
import '../pane/all_panes_store.dart';
import '../pane/pane_store.dart';
import '../rename/rename_store.dart';
import '../root_store.dart';

/// Extensions on BuildContext to easily access dependencies
extension ContextExtensions on BuildContext {
  /// Get the RootStore from the context
  RootStore get rootStore => Provider.of<RootStore>(this, listen: false);

  /// Get the CommandRepository from the context
  CommandRepository get commandRepository => rootStore.commandRepository;

  /// Get the KeyBindManager from the context
  KeyBindManager get keyBindManager => rootStore.keyBindManager;

  /// Get the CommandDispatcher from the context
  CommandDispatcher get commandDispatcher => rootStore.commandDispatcher;

  CommandContextRepository get commandContextRepository => rootStore.commandContextRepository;

  /// Get the RenameStore from the context
  RenameStore get renameStore => rootStore.renameStore;

  /// Get the AllPanesStore from the context
  AllPanesStore get allPanesStore => rootStore.allPanesStore;

  /// Get the PaneStore from the context
  PaneStore get paneStore => Provider.of<PaneStore>(this, listen: false);

  /// Get the DirectoryViewStore from the context
  DirectoryViewStore get directoryViewStore => Provider.of<DirectoryViewStore>(this, listen: false);

  /// Get the HistoryStore from the context
  HistoryStore get historyStore => Provider.of<PaneStore>(this, listen: false).historyStore;

  /// Get the SettingsRepository from the context
  SettingsRepository get settingsRepository => rootStore.settingsRepository;

  /// Get the overlay state from the context
  OverlayState get overlay => Overlay.of(this);

  /// Get the text direction from the context
  TextDirection get textDirection => Directionality.of(this);
}
