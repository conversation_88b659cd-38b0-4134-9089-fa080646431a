import 'package:meta/meta.dart';
import 'package:mobx/mobx.dart';

import '../directory_view/directory_view_store.dart';
import '../directory_view/navigation_bar_store.dart';
import '../domain/side.dart';
import '../history/history_store.dart';
import './all_panes_store.dart';

part '.gen/pane_store.g.dart';

class PaneStore = PaneStoreBase with _$PaneStore;

abstract class PaneStoreBase with Store {
  final HistoryStore historyStore;
  final DirectoryViewStore directoryViewStore;
  final NavigationBarStore navigationBarStore;
  final Side side;

  late final AllPanesStore allPanesStore;

  PaneStoreBase(this.historyStore, this.directoryViewStore, this.navigationBarStore, this.side);

  // Method to set the AllPanesStore reference after initialization
  void setAllPanesStore(AllPanesStore store) {
    allPanesStore = store;
  }

  bool get isSource => allPanesStore.sourcePaneSide == side;

  @action
  void makeSource() {
    allPanesStore.setSourcePane(side);
  }

  @visibleForTesting
  void dispose() {
    historyStore.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    directoryViewStore.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    navigationBarStore.dispose();
  }
}
