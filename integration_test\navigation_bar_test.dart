import 'package:flutter/material.dart' hide NavigationBar;
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/util/setup.dart';

import 'test_helpers.dart';

/// Helper function to open navigation bar input mode using F1 key
Future<void> openNavigationBarWithF1(WidgetTester tester) async {
  await tester.sendKeyEvent(LogicalKeyboardKey.f1);
  await tester.pumpAndSettle();
}

/// Helper function to open navigation bar input mode by tapping on empty space
Future<void> openNavigationBarByTapping(WidgetTester tester, {Side side = Side.left}) async {
  final navigationBarKey = Key('navigation-bar-empty-space-${side.name}');
  final gestureDetectorFinder = find.byKey(navigationBarKey);
  expect(gestureDetectorFinder, findsOneWidget, reason: 'Navigation bar for ${side.name} should be present');

  await tester.tap(gestureDetectorFinder);
  await tester.pumpAndSettle();
}

/// Helper function to close navigation bar by pressing Escape
Future<void> closeNavigationBarWithEscape(WidgetTester tester) async {
  await tester.sendKeyEvent(LogicalKeyboardKey.escape);
  await tester.pumpAndSettle();
}

/// Helper function to close navigation bar by clicking outside
Future<void> closeNavigationBarByClickingOutside(WidgetTester tester, {Side side = Side.left}) async {
  // Click on the file list area which should be outside the navigation bar
  final fileListKey = Key('file-list-${side.name}');
  final fileListFinder = find.byKey(fileListKey);
  expect(fileListFinder, findsOneWidget, reason: 'File list for ${side.name} should be present');

  await tester.tap(fileListFinder);
  await tester.pumpAndSettle();
}

/// Helper function to check if a navigation bar is in input mode
bool isNavigationBarInInputMode(WidgetTester tester, {Side side = Side.left}) {
  // When in input mode, the navigation bar shows a TextField instead of the empty space GestureDetector
  // Look for a TextField in the navigation bar area

  // First, try to find the empty space key - if it exists, we're NOT in input mode
  final navigationBarKey = Key('navigation-bar-empty-space-${side.name}');
  final gestureDetectorFinder = find.byKey(navigationBarKey);
  if (gestureDetectorFinder.evaluate().isNotEmpty) {
    return false; // Empty space is visible, so not in input mode
  }

  // If empty space is not visible, check if there's a TextField in the navigation bar area
  // We can look for any TextField that might be the navigation bar input
  final textFieldFinder = find.byType(TextField);
  return textFieldFinder.evaluate().isNotEmpty;
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  // Test files with explicit timestamps for consistent golden files
  final testFiles = [
    TestFile(
      name: 'documents',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
      children: [
        TestFile(
          name: 'file1.txt',
          modifyTime: DateTime(2024, 1, 1, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'downloads',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
      children: [
        TestFile(
          name: 'file2.txt',
          modifyTime: DateTime(2024, 1, 2, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'readme.txt',
      modifyTime: DateTime(2024, 1, 3, 10, 0, 0),
    ),
  ];

  group('Navigation Bar Input Mode Tests', () {
    testWidgets('F1 key should open navigation bar input mode', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_f1_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Open the navigation bar using F1 key
      await openNavigationBarWithF1(tester);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode after pressing F1');
    });

    testWidgets('Clicking on empty space in navigation bar should open input mode', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_click_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Open the navigation bar by clicking on empty space
      await openNavigationBarByTapping(tester, side: Side.left);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue,
          reason: 'Navigation bar should be in input mode after clicking on empty space');
    });

    testWidgets('Escape key should close navigation bar input mode', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_escape_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // First open the navigation bar
      await openNavigationBarWithF1(tester);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode after opening');

      // Close it with Escape key
      await closeNavigationBarWithEscape(tester);

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isFalse,
          reason: 'Navigation bar should not be in input mode after pressing Escape');
    });

    testWidgets('Clicking outside should close navigation bar input mode', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_outside_click_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // First open the navigation bar
      await openNavigationBarWithF1(tester);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode after opening');

      // Close it by clicking outside
      await closeNavigationBarByClickingOutside(tester, side: Side.left);

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isFalse,
          reason: 'Navigation bar should not be in input mode after clicking outside');
    });

    testWidgets('Commands should not react when navigation bar is focused', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_command_isolation_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // First, select a file using arrow keys to ensure we have a focused file
      await testState.sendKeyAndWait(LogicalKeyboardKey.arrowDown);

      // Verify a file is selected
      expect(testState.directoryViewStore(Side.left).focusedRowIndex, greaterThan(0), reason: 'A file should be selected after arrow down');

      // Open the navigation bar input mode
      await openNavigationBarWithF1(tester);
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode');

      // Try F2 (rename) - it should not work when navigation bar is focused
      await tester.sendKeyEvent(LogicalKeyboardKey.f2);
      await tester.pumpAndSettle();

      // Verify that rename mode was not triggered (navigation bar should still be in input mode)
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue,
          reason: 'Navigation bar should remain in input mode - F2 should not work');

      // Also verify that the file is not in rename mode
      expect(testState.directoryViewStore(Side.left).renameStore.isRenaming, isFalse,
          reason: 'File should not be in rename mode when navigation bar is focused');
    });
  });
}