import 'package:flutter/material.dart' hide NavigationBar;
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qfiler/presentation/directory_view/widgets/navigation_bar.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/util/setup.dart';

import 'test_helpers.dart';

/// Helper function to tap on a navigation bar to enter input mode
Future<void> tapNavigationBar(WidgetTester tester) async {
  final navigationBarFinder = find.byType(NavigationBar);
  expect(navigationBarFinder, findsAtLeastNWidgets(1), reason: 'Navigation bar should be present');

  // Find the breadcrumbs (RichText) in the first navigation bar (left pane)
  final breadcrumbsFinder = find.descendant(
    of: navigationBarFinder.first,
    matching: find.byType(RichText),
  );
  expect(breadcrumbsFinder, findsAtLeastNWidgets(1), reason: 'Breadcrumbs should be present in navigation bar');

  // Get the breadcrumbs widget and tap on the rightmost part of it
  // This should correspond to the last breadcrumb item which calls showInput()
  final breadcrumbs = breadcrumbsFinder.first;
  final breadcrumbsRect = tester.getRect(breadcrumbs);

  // Tap on the right side of the breadcrumbs (last breadcrumb item)
  final tapOffset = Offset(breadcrumbsRect.right - 10, breadcrumbsRect.center.dy);
  await tester.tapAt(tapOffset);
  await tester.pumpAndSettle();
}

/// Helper function to check if a navigation bar is in input mode
bool isNavigationBarInInputMode(WidgetTester tester) {
  final navigationBarFinder = find.byType(NavigationBar);
  if (navigationBarFinder.evaluate().isEmpty) return false;

  // Check the first navigation bar (left pane) for input mode
  final textFieldFinder = find.descendant(
    of: navigationBarFinder.first,
    matching: find.byType(TextField),
  );
  return textFieldFinder.evaluate().isNotEmpty;
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  // Test files with explicit timestamps for consistent golden files
  final testFiles = [
    TestFile(
      name: 'documents',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
      children: [
        TestFile(
          name: 'file1.txt',
          modifyTime: DateTime(2024, 1, 1, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'downloads',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
      children: [
        TestFile(
          name: 'file2.txt',
          modifyTime: DateTime(2024, 1, 2, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'readme.txt',
      modifyTime: DateTime(2024, 1, 3, 10, 0, 0),
    ),
  ];

  group('Navigation Bar Auto Complete Tests', () {
    testWidgets('Auto complete dropdown should open when clicking on empty space', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_autocomplete_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Click on the navigation bar to open the auto complete dropdown
      await tapNavigationBar(tester);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester), isTrue, reason: 'Navigation bar should be in input mode after clicking');
    });

    testWidgets('Auto complete dropdown should close when pressing escape', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_escape_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Click on the navigation bar to open the auto complete dropdown
      await tapNavigationBar(tester);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester), isTrue, reason: 'Navigation bar should be in input mode after clicking');

      // Press escape to close the dropdown
      await tester.sendKeyEvent(LogicalKeyboardKey.escape);
      await tester.pumpAndSettle();

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester), isFalse, reason: 'Navigation bar should not be in input mode after pressing escape');
    });

    testWidgets('Auto complete dropdown should close when losing focus', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_focus_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Click on the navigation bar to open the auto complete dropdown
      await tapNavigationBar(tester);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester), isTrue, reason: 'Navigation bar should be in input mode after clicking');

      // Tap on a ListView to lose focus
      final listViewFinder = find.byType(ListView);
      expect(listViewFinder, findsAtLeastNWidgets(1), reason: 'ListView should be present');
      await tester.tap(listViewFinder.first);
      await tester.pumpAndSettle();

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester), isFalse, reason: 'Navigation bar should not be in input mode after losing focus');
    });

    testWidgets('Commands should not react when navigation bar is focused', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_command_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Ensure a file is selected in the left pane (where we'll open the navigation bar)
      final leftDirectoryViewStore = testState.directoryViewStore(Side.left);
      expect(leftDirectoryViewStore.files.isNotEmpty, isTrue, reason: 'Left pane should have files');

      // Select the first file
      final firstFile = leftDirectoryViewStore.files.first;
      leftDirectoryViewStore.setFocusedFile(firstFile);
      await tester.pumpAndSettle();

      // Verify a file is selected
      expect(leftDirectoryViewStore.focusedRowIndex, greaterThanOrEqualTo(0), reason: 'A file should be selected');

      // Open the navigation bar input mode
      await tapNavigationBar(tester);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester), isTrue, reason: 'Navigation bar should be in input mode after clicking');

      // Check if any file is currently being renamed (should be false initially)
      final renameStore = testState.rootStore.renameStore;
      expect(renameStore.isRenaming, isFalse, reason: 'No file should be in rename mode initially');

      // Press F2 (rename command) while navigation bar is focused
      await tester.sendKeyEvent(LogicalKeyboardKey.f2);
      await tester.pumpAndSettle();

      // Verify that F2 did not trigger rename mode (command should be ignored)
      expect(renameStore.isRenaming, isFalse, reason: 'F2 should not trigger rename when navigation bar is focused');

      // Verify that the navigation bar is still in input mode
      expect(isNavigationBarInInputMode(tester), isTrue, reason: 'Navigation bar should still be in input mode');
    });
  });
}