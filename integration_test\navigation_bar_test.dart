import 'package:flutter/material.dart' hide NavigationBar;
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:qfiler/presentation/domain/side.dart';
import 'package:qfiler/presentation/util/setup.dart';

import 'test_helpers.dart';

/// Helper function to tap on a navigation bar to enter input mode
Future<void> tapNavigationBar(WidgetTester tester, {Side side = Side.left}) async {
  // Find the specific navigation bar by side
  final navigationBarKey = Key('navigation-bar-empty-space-${side.name}');
  final gestureDetectorFinder = find.byKey(navigationBarKey);
  expect(gestureDetectorFinder, findsOneWidget, reason: 'Navigation bar for ${side.name} should be present');

  // Find the refresh button inside the navigation bar
  final refreshButtonFinder = find.descendant(
    of: gestureDetectorFinder,
    matching: find.byIcon(Icons.refresh),
  );
  expect(refreshButtonFinder, findsOneWidget, reason: 'Refresh button should be present in ${side.name} navigation bar');

  // Get the GestureDetector rect and refresh button rect
  final gestureDetectorRect = tester.getRect(gestureDetectorFinder);
  final refreshButtonRect = tester.getRect(refreshButtonFinder);

  // Tap in the space between the breadcrumbs and the refresh button
  // This should be empty space that triggers the GestureDetector's onTap
  final tapOffset = Offset(
    refreshButtonRect.left - 20, // 20 pixels to the left of the refresh button
    gestureDetectorRect.center.dy,
  );
  await tester.tapAt(tapOffset);
  await tester.pumpAndSettle();
}

/// Helper function to check if a navigation bar is in input mode
bool isNavigationBarInInputMode(WidgetTester tester, {Side side = Side.left}) {
  final navigationBarKey = Key('navigation-bar-empty-space-${side.name}');
  final gestureDetectorFinder = find.byKey(navigationBarKey);
  if (gestureDetectorFinder.evaluate().isEmpty) return false;

  // Check the specific navigation bar for input mode
  final textFieldFinder = find.descendant(
    of: gestureDetectorFinder,
    matching: find.byType(TextField),
  );
  return textFieldFinder.evaluate().isNotEmpty;
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  // Test files with explicit timestamps for consistent golden files
  final testFiles = [
    TestFile(
      name: 'documents',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
      children: [
        TestFile(
          name: 'file1.txt',
          modifyTime: DateTime(2024, 1, 1, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'downloads',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
      children: [
        TestFile(
          name: 'file2.txt',
          modifyTime: DateTime(2024, 1, 2, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'readme.txt',
      modifyTime: DateTime(2024, 1, 3, 10, 0, 0),
    ),
  ];

  group('Navigation Bar Auto Complete Tests', () {
    testWidgets('Auto complete dropdown should open when clicking on empty space', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_autocomplete_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Click on the navigation bar to open the auto complete dropdown
      await tapNavigationBar(tester, side: Side.left);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode after clicking');
    });

    testWidgets('Auto complete dropdown should close when pressing escape', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_escape_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Click on the navigation bar to open the auto complete dropdown
      await tapNavigationBar(tester, side: Side.left);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode after clicking');

      // Press escape to close the dropdown
      await tester.sendKeyEvent(LogicalKeyboardKey.escape);
      await tester.pumpAndSettle();

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isFalse,
          reason: 'Navigation bar should not be in input mode after pressing escape');
    });

    testWidgets('Auto complete dropdown should close when losing focus', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_focus_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Click on the navigation bar to open the auto complete dropdown
      await tapNavigationBar(tester, side: Side.left);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode after clicking');

      // Tap on a ListView to lose focus
      final listViewFinder = find.byType(ListView);
      expect(listViewFinder, findsAtLeastNWidgets(1), reason: 'ListView should be present');
      await tester.tap(listViewFinder.first);
      await tester.pumpAndSettle();

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isFalse,
          reason: 'Navigation bar should not be in input mode after losing focus');
    });

    testWidgets('Commands should not react when navigation bar is focused', (tester) async {
      final testState = await prepareTest(
        tester,
        name: 'navigation_bar_command_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Use arrow keys to select a file in the left pane (black box testing)
      // The left pane should be focused by default, so arrow down should select the first file
      await testState.sendKeyAndWait(LogicalKeyboardKey.arrowDown);

      // Verify a file is selected by checking the focused row index
      expect(testState.directoryViewStore(Side.left).focusedRowIndex, greaterThan(0), reason: 'A file should be selected after arrow down');

      // Open the navigation bar input mode on the left side
      await tapNavigationBar(tester, side: Side.left);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue, reason: 'Navigation bar should be in input mode after clicking');

      // Press F2 (rename command) while navigation bar is focused
      await tester.sendKeyEvent(LogicalKeyboardKey.f2);
      await tester.pumpAndSettle();

      // Verify that the navigation bar is still in input mode (F2 was ignored)
      expect(isNavigationBarInInputMode(tester, side: Side.left), isTrue,
          reason: 'Navigation bar should still be in input mode after F2 (command should be ignored)');

      // Additional verification: check that no rename dialog appeared
      // The only TextField should be the navigation bar's TextField, not a rename TextField
      expect(find.byType(TextField), findsOneWidget, reason: 'Only navigation bar TextField should be present, no rename TextField');
    });
  });
}