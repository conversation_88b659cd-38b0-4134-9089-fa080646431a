import 'package:flutter/material.dart' hide NavigationBar;
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:qfiler/presentation/directory_view/widgets/navigation_bar.dart';
import 'package:qfiler/presentation/util/setup.dart';
import 'test_helpers.dart';

/// Helper function to tap on a navigation bar
Future<void> tapNavigationBar(WidgetTester tester) async {
  final navigationBarFinder = find.byType(NavigationBar);
  expect(navigationBarFinder, findsAtLeastNWidgets(1), reason: 'Navigation bar should be present');

  final gestureDetectorFinder = find.byKey(const Key('navigation-bar-empty-space'));
  expect(gestureDetectorFinder, findsOneWidget, reason: 'GestureDetector should be present in navigation bar');

  await tester.tap(gestureDetectorFinder);
  await tester.pumpAndSettle();
}

/// Helper function to check if a navigation bar is in input mode
bool isNavigationBarInInputMode(WidgetTester tester) {
  final navigationBarFinder = find.byType(NavigationBar);
  if (navigationBarFinder.evaluate().isEmpty) return false;

  final textFieldFinder = find.descendant(
    of: navigationBarFinder.first,
    matching: find.byType(TextField),
  );
  return textFieldFinder.evaluate().isNotEmpty;
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  setUpAll(() => setupApp());

  // Test files with explicit timestamps for consistent golden files
  final testFiles = [
    TestFile(
      name: 'documents',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 1, 10, 0, 0),
      children: [
        TestFile(
          name: 'file1.txt',
          modifyTime: DateTime(2024, 1, 1, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'downloads',
      isDirectory: true,
      modifyTime: DateTime(2024, 1, 2, 10, 0, 0),
      children: [
        TestFile(
          name: 'file2.txt',
          modifyTime: DateTime(2024, 1, 2, 11, 0, 0),
        ),
      ],
    ),
    TestFile(
      name: 'readme.txt',
      modifyTime: DateTime(2024, 1, 3, 10, 0, 0),
    ),
  ];

  group('Navigation Bar Auto Complete Tests', () {
    testWidgets('Auto complete dropdown should open when clicking on empty space', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_autocomplete_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Click on the navigation bar to open the auto complete dropdown
      await tapNavigationBar(tester);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester), isTrue, reason: 'Navigation bar should be in input mode after clicking');
    });

    testWidgets('Auto complete dropdown should close when pressing escape', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_escape_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Click on the navigation bar to open the auto complete dropdown
      await tapNavigationBar(tester);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester), isTrue, reason: 'Navigation bar should be in input mode after clicking');

      // Press escape to close the dropdown
      await tester.sendKeyEvent(LogicalKeyboardKey.escape);
      await tester.pumpAndSettle();

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester), isFalse, reason: 'Navigation bar should not be in input mode after pressing escape');
    });

    testWidgets('Auto complete dropdown should close when losing focus', (tester) async {
      await prepareTest(
        tester,
        name: 'navigation_bar_focus_test',
        leftFiles: testFiles,
        rightFiles: testFiles,
      );

      // Click on the navigation bar to open the auto complete dropdown
      await tapNavigationBar(tester);

      // Verify that the navigation bar is in input mode
      expect(isNavigationBarInInputMode(tester), isTrue, reason: 'Navigation bar should be in input mode after clicking');

      // Tap on a ListView to lose focus
      final listViewFinder = find.byType(ListView);
      expect(listViewFinder, findsAtLeastNWidgets(1), reason: 'ListView should be present');
      await tester.tap(listViewFinder.first);
      await tester.pumpAndSettle();

      // Verify that the navigation bar is no longer in input mode
      expect(isNavigationBarInInputMode(tester), isFalse, reason: 'Navigation bar should not be in input mode after losing focus');
    });
  });
}