import 'package:flutter/foundation.dart';

import '../../app/notification/notification_store.dart';
import '../../app/task/task.dart';
import '../../app/task/task_store.dart';
import '../../app/util/log_utils.dart';
import '../directory_view/directory_view_store.dart';
import '../directory_view/domain/display_file.dart';
import '../directory_view/navigation_bar_store.dart';
import '../pane/all_panes_store.dart';
import '../pane/pane_store.dart';
import 'command.dart';
import 'command_context.dart';
import 'command_context_repository.dart';
import 'command_id.dart';
import 'command_palette.dart';

/// Dispatches commands to their appropriate handlers
class CommandDispatcher {
  CommandDispatcher({
    required this.allPanesStore,
    required this.commandContextRepository,
    required this.jobStore,
    required this.notificationStore,
  });

  final AllPanesStore allPanesStore;
  final CommandContextRepository commandContextRepository;
  final JobStore jobStore;
  final NotificationStore notificationStore;

  /// Get the currently active PaneStore
  PaneStore get activePaneStore => allPanesStore.sourcePane;

  /// Get the currently active DirectoryViewStore
  DirectoryViewStore get activeDirectoryViewStore => activePaneStore.directoryViewStore;

  /// Get the currently active NavigationBarStore
  NavigationBarStore get activeNavigationBarStore => activePaneStore.navigationBarStore;

  /// Dispatch a command
  bool dispatchCommand(Command command) {
    CommandContext context = commandContextRepository.currentContext;
    if (kDebugMode) {
      logger.fine('dispatchCommand($command): $context');
    }

    // dispatchCommand should usually be called only with an applicable command,
    // but calling it from the command palette does not check for applicability.
    if (!command.isApplicableInContext(commandContextRepository.currentContext)) {
      if (kDebugMode) {
        logger.warning('dispatchCommand($command): Not applicable in $context');
      }
      return false;
    }

    switch (command.id) {
      case CommandId.showCommandPalette:
        return _showCommandPalette();
      case CommandId.renameFile:
        return _renameFile();
      case CommandId.copyFiles:
        return _copyFiles();
      case CommandId.moveFiles:
        return _moveFiles();
      case CommandId.deleteFiles:
        return _deleteFiles();
      case CommandId.navigateToParent:
        return _navigateToParent();
      case CommandId.navigateUp:
        return _navigateUp();
      case CommandId.navigateDown:
        return _navigateDown();
      case CommandId.navigateBack:
        return _navigateBack();
      case CommandId.navigateForward:
        return _navigateForward();
      case CommandId.openNavigationBar:
        return _openNavigationBar();
      case CommandId.switchPane:
        return _switchPane();
      case CommandId.executeFile:
        return _executeFile();
      case CommandId.toggleSelection:
        return _toggleSelection();
      case CommandId.refreshDirectory:
        return _refreshDirectory();
    }
  }

  bool _showCommandPalette() {
    // Open the command palette using the global key
    commandPaletteKey.currentState?.openCommandPalette();
    return true;
  }

  bool _renameFile() {
    final store = activeDirectoryViewStore;
    store.startRenameFocusedFile();
    return true;
  }

  bool _copyFiles() {
    try {
      final sourceStore = allPanesStore.sourcePane.directoryViewStore;
      final targetStore = allPanesStore.targetPane.directoryViewStore;

      // Get selected files from the source pane
      final selectedFiles = sourceStore.selectedFiles.toList();
      if (selectedFiles.isEmpty) {
        final focusedFile = sourceStore.focusedFile.value;
        assert(focusedFile != null, 'Focused file should not be null');
        if (focusedFile == null) {
          notificationStore.notifyWarn('No files selected for copying');
          return false;
        }
        if (focusedFile is ParentDirDisplayFile) {
          notificationStore.notifyWarn('Parent directory cannot be copied');
          return false;
        }
        selectedFiles.add(focusedFile as FileDisplayFile);
      }

      // Convert DisplayFiles to Files
      final filesToCopy = selectedFiles.map((displayFile) => displayFile.file).toList();

      // Get destination directory from target pane
      final destinationDir = targetStore.dir;

      // Create a copy job specification
      jobStore.addJobSpec(JobSpec.copy(
        files: filesToCopy,
        destDir: destinationDir,
      ));

      if (kDebugMode) {
        logger.info('_copyFiles(): Started copy job for ${filesToCopy.length} files to $destinationDir');
        for (final file in filesToCopy) {
          logger.info('  - ${file.path}');
        }
      }

      return true;
    } catch (error) {
      notificationStore.notifyError(error);
      return false;
    }
  }

  bool _moveFiles() {
    try {
      final sourceStore = allPanesStore.sourcePane.directoryViewStore;
      final targetStore = allPanesStore.targetPane.directoryViewStore;

      // Get selected files from the source pane
      final selectedFiles = sourceStore.selectedFiles.toList();

      if (selectedFiles.isEmpty) {
        notificationStore.notifyWarn('No files selected for moving');
        return false;
      }

      // Convert DisplayFiles to Files
      final filesToMove = selectedFiles.map((displayFile) => displayFile.file).toList();

      // Get destination directory from target pane
      final destinationDir = targetStore.dir;

      // Create a move job specification
      jobStore.addJobSpec(JobSpec.move(
        files: filesToMove,
        destDir: destinationDir,
      ));

      if (kDebugMode) {
        logger.info('_moveFiles(): Started move job for ${filesToMove.length} files to $destinationDir');
        for (final file in filesToMove) {
          logger.info('  - ${file.path}');
        }
      }

      return true;
    } catch (error) {
      notificationStore.notifyError(error);
      return false;
    }
  }

  bool _deleteFiles() {
    // TODO: Implement delete files
    if (kDebugMode) {
      logger.info('Delete files not implemented yet');
    }
    return false;
  }

  bool _navigateToParent() {
    final store = activeDirectoryViewStore;
    store.goToParentDir();
    return true;
  }

  bool _navigateUp() {
    final store = activeDirectoryViewStore;
    store.moveFocusUp();
    return true;
  }

  bool _navigateDown() {
    final store = activeDirectoryViewStore;
    store.moveFocusDown();
    return true;
  }

  bool _navigateBack() {
    final store = activeDirectoryViewStore;
    store.goBackInHistory();
    return true;
  }

  bool _navigateForward() {
    final store = activeDirectoryViewStore;
    store.goForwardInHistory();
    return true;
  }

  bool _openNavigationBar() {
    final navigationBarStore = activeNavigationBarStore;
    navigationBarStore.showInput();
    if (kDebugMode) {
      logger.info('Open navigation bar command triggered for ${activePaneStore.side}');
    }
    return true;
  }

  bool _switchPane() {
    allPanesStore.switchSourcePane();
    return true;
  }

  bool _executeFile() {
    final store = activeDirectoryViewStore;
    final selectedFile = store.focusedFile.value;
    if (selectedFile == null) {
      if (kDebugMode) {
        logger.warning('_executeFile(): No file selected');
      }
      return false;
    }

    store.executeFile(selectedFile);
    return true;
  }

  bool _toggleSelection() {
    // TODO: Move all this logic into the store
    final focusedFileAsync = activeDirectoryViewStore.focusedFile;
    if (focusedFileAsync.isLoading || focusedFileAsync.error != null || focusedFileAsync.value == null) {
      return false;
    }

    final focusedFile = focusedFileAsync.value!;

    // Don't allow selecting the parent directory
    if (focusedFile is ParentDirDisplayFile) return false;

    // Toggle selection of the focused file
    if (activeDirectoryViewStore.selectedFiles.contains(focusedFile)) {
      activeDirectoryViewStore.selectedFiles.remove(focusedFile);
    } else {
      activeDirectoryViewStore.selectedFiles.add(focusedFileAsync as FileDisplayFile);
    }

    return true;
  }

  bool _refreshDirectory() {
    final store = activeDirectoryViewStore;
    store.reload();
    return true;
  }

  @visibleForTesting
  void dispose() {
    allPanesStore.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    commandContextRepository.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    jobStore.dispose(); // ignore: invalid_use_of_visible_for_testing_member
    notificationStore.dispose(); // ignore: invalid_use_of_visible_for_testing_member
  }

  static final logger = loggerFor(CommandDispatcher, Level.INFO);
}
