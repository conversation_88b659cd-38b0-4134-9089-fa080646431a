import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mobx/mobx.dart';

import '../../app/domain/file.dart';
import '../../app/domain/path.dart';
import '../../app/file_system/file_repository.dart';
import '../../app/util/async_value.dart';
import '../../app/util/log_utils.dart';
import '../../app/util/quick_score.dart';
import '../command/command_context.dart';
import '../command/command_context_repository.dart';
import '../util/widgets/auto_complete_text_field.dart';

part '.gen/navigation_bar_store.g.dart';

class NavigationBarStore = NavigationBarStoreBase with _$NavigationBarStore;

abstract class NavigationBarStoreBase with Store {
  final FileRepository _fileRepository;
  final CommandContextRepository _commandContextRepository;
  late final WatchFileSession _autoCompleteSession;

  NavigationBarStoreBase({
    required FileRepository fileRepository,
    required CommandContextRepository commandContextRepository,
  })  : _fileRepository = fileRepository,
        _commandContextRepository = commandContextRepository {
    _autoCompleteSession = _fileRepository.createWatchSession();
  }

  /// Whether the navigation bar is in input mode (showing text field)
  @observable
  bool isInputVisible = false;

  /// The current directory for which this navigation bar is showing
  @observable
  RawPath currentDirectory = RawPath.empty;

  /// Index of the currently hovered breadcrumb item (-1 for ellipsis)
  @observable
  int? hoveredBreadcrumbIndex;

  @action
  void showInput() {
    if (kDebugMode) {
      logger.fine('[$currentDirectory] showInput()');
    }
    isInputVisible = true;
  }

  @action
  void hideInput() {
    if (kDebugMode) {
      logger.fine('[$currentDirectory] hideInput()');
    }
    isInputVisible = false;
  }

  @action
  void setCurrentDirectory(RawPath directory) {
    if (currentDirectory != directory) {
      if (kDebugMode) {
        logger.fine('[$currentDirectory] setCurrentDirectory($directory)');
      }
      currentDirectory = directory;
    }
  }

  @action
  void setBreadcrumbHover(int? index) {
    hoveredBreadcrumbIndex = index;
  }

  /// Calculate autocomplete suggestions for the given prefix
  ///
  /// FIXME: How to handle '/'?
  /// FIXME: E:\qfi highlights the wrong bits of text.
  /// FIXME: Any prefix that matches a directory exactly should be the first suggestion with a separator at the end.
  /// FIXME: All suggestions should have a separator at the end.
  /// FIXME: Going back to E:\qfiler throws an error and marks the wrong portion of the suggestion as a match. This is because the match is larger than the str length.
  /// FIXME: The empty string throws an error.
  /// FIXME: If prefix does not end with a separator and is a valid directory, first suggestion should be prefix with separator, then its children.
  AsyncValue<AutoCompleteSuggestions<File>> calcAutoCompleteSuggestions(String prefix) {
    if (kDebugMode) {
      logger.finest('[$currentDirectory] calcAutoCompleteSuggestions($prefix)');
    }

    if (prefix.isEmpty) {
      return AsyncValueSnapshot(value: AutoCompleteSuggestions(const []));
    }

    final unresolvedPrefix = prefix.asPath(resolve: false);
    final resolvedPrefix = prefix.isNotEmpty ? unresolvedPrefix.resolved() : currentDirectory;
    final parent = resolvedPrefix.parent;

    final RawPath dirToFetch;
    final String? nameToMatch;
    if (prefix.isEmpty || prefix.endsWith(RawPath.pathSeparator) || parent == null) {
      // Fetch the directory that the resolvedPrefix points to and mark all its children as matches.
      dirToFetch = resolvedPrefix;
      nameToMatch = null;
    } else {
      dirToFetch = parent;
      nameToMatch = resolvedPrefix.name;
    }

    if (kDebugMode) {
      logger.finest(
          '[$currentDirectory] calcAutoCompleteSuggestions($prefix): dirToFetch=$dirToFetch, nameToMatch=$nameToMatch, unresolvedPrefix=$unresolvedPrefix, resolvedPrefix=$resolvedPrefix');
    }

    // Add matches of the prefix from the parent.
    // _autoCompleteSession.watchDir is safe to call multiple times because result of watching is
    // cached and only performed once. On top of that, it is limited to watch at most 5 directories
    // after which it starts un-watching the oldest ones.
    final currentDirFiles = _autoCompleteSession.watchDir(dirToFetch);
    if (!currentDirFiles.hasValue) {
      // Return loading or error.
      return _autoCompleteResultWithoutMatches(currentDirFiles, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix);
    }

    final files = currentDirFiles.value!;

    // If prefix matches the name of child directory exactly, add it as the first suggestion and
    // add its children below it in the results.
    final fileInParent = nameToMatch != null ? currentDirFiles.get(nameToMatch) : null;
    if (fileInParent != null && fileInParent.isDirectory) {
      // We have an exact match.
      final childFiles = _autoCompleteSession.watchDir(resolvedPrefix);
      if (!childFiles.hasValue) {
        // return childFiles;
        return _autoCompleteResultWithoutMatches(childFiles, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix);
      }

      final childFilesList = childFiles.value!;
      final childSuggestions = childFilesList
          .where((e) => e.isDirectory)
          .map((e) => _replacePrefixOfSuggestion(e, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix))
          .toList();

      final parentSuggestion = _replacePrefixOfSuggestion(fileInParent, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix);

      return AsyncValueSnapshot(
          value: AutoCompleteSuggestions([
        SuggestionItem(parentSuggestion),
        ...childSuggestions.map((e) => SuggestionItem(e)),
      ]));
    }

    // Filter files by nameToMatch if provided
    final filteredFiles = nameToMatch != null ? files.where((e) => e.name.toLowerCase().startsWith(nameToMatch.toLowerCase())).toList() : files;

    // Use quick score for fuzzy matching
    final results = nameToMatch != null
        ? quickScore(
            filteredFiles.where((e) => e.isDirectory).toList(),
            nameToMatch,
            (file) => file.name,
          )
        : filteredFiles.where((e) => e.isDirectory).map((e) => QuickScoreResult(e, const [])).toList();

    // We only match the last element of the prefix, so we must adjust the match ranges.
    final prefixLength = nameToMatch == null ? unresolvedPrefix.absolutePath.length : unresolvedPrefix.dir.length + RawPath.pathSeparator.length;

    return AsyncValueSnapshot(
        value: AutoCompleteSuggestions(results.map((e) {
      return SuggestionItem(_replacePrefixOfSuggestion(e.item, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix),
          matches: e.matches.map((match) {
            return TextRange(start: match.start + prefixLength, end: match.end + prefixLength);
          }).toList());
    }).toList()));
  }

  AsyncValue<AutoCompleteSuggestions<File>> _autoCompleteResultWithoutMatches(AsyncValue<List<File>> files,
          {required RawPath unresolvedPrefix, required RawPath resolvedPrefix}) =>
      files.snapshot.map((value) {
        return AutoCompleteSuggestions(value
            .where((e) => e.isDirectory)
            .map((e) => SuggestionItem(_replacePrefixOfSuggestion(e, unresolvedPrefix: unresolvedPrefix, resolvedPrefix: resolvedPrefix)))
            .toList());
      });

  File _replacePrefixOfSuggestion(File suggestion, {required RawPath unresolvedPrefix, required RawPath resolvedPrefix}) {
    // Replace the resolved prefix with the unresolved prefix in the suggestion path
    final suggestionPath = suggestion.path;
    if (suggestionPath.startsWith(resolvedPrefix)) {
      final suffix = suggestionPath.relativeTo(resolvedPrefix);
      final newPath = unresolvedPrefix.append(suffix);
      return suggestion.withPath(newPath);
    }
    return suggestion;
  }

  void startAutoComplete() {
    if (kDebugMode) {
      logger.fine('[$currentDirectory] startAutoComplete()');
    }
    _commandContextRepository.pushContext(const CommandContext(inNavigationBarInput: true));
  }

  void stopAutoComplete() {
    if (kDebugMode) {
      logger.fine('[$currentDirectory] stopAutoComplete()');
    }
    _autoCompleteSession.unwatchAll();
    _commandContextRepository.popContext(const CommandContext(inNavigationBarInput: true));
  }

  void dispose() {
    if (kDebugMode) {
      logger.fine('[$currentDirectory] dispose()');
    }
    _autoCompleteSession.unwatchAll();
  }

  static final logger = loggerFor(NavigationBarStore, Level.FINE);
}
