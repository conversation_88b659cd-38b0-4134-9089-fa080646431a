import 'dart:async';
import 'dart:io' as io;
import 'dart:typed_data';

import 'package:path/path.dart' as p;
import 'package:qfiler/app/domain/path.dart';
import 'package:qfiler/app/file_system/file_system.dart';
import 'package:qfiler/rust/.gen/api/domain.dart' as domain;

/// A fake implementation of the FileSystem for testing purposes.
/// This allows simulating file system operations without interacting with the actual file system.
class InMemoryFileSystem implements FileSystem {
  // In-memory representation of the file system: path -> content (for files) or null (for directories)
  final Map<String, _FileSystemEntry> _entries = {};

  // Helper to normalize paths (e.g., handle different separators)
  String _normalizePath(RawPath path) {
    // Simple normalization: convert to lowercase and replace backslashes with forward slashes
    return path.absolutePath.toLowerCase().replaceAll('\\', '/');
  }

  // Helper to get a file system entry, returning null if not found
  // This method is kept for future use
  // ignore: unused_element
  _FileSystemEntry? _getEntry(RawPath path) {
    return _entries[_normalizePath(path)];
  }

  // Helper to ensure parent directory exists by checking and throwing errors if not valid.
  // Ensures parent directory hierarchy exists, creating it if necessary.
  void _createParentDirHierarchyIfNeeded(RawPath path) {
    final parentPath = path.parent;
    if (parentPath != null && parentPath.absolutePath != path.absolutePath) {
      // Check it's not root
      _createParentDirHierarchyIfNeeded(parentPath); // Recursive call for the parent's parent

      final normalizedParent = _normalizePath(parentPath);
      final parentEntry = _entries[normalizedParent];

      if (parentEntry == null) {
        _entries[normalizedParent] = _FileSystemEntry(isDirectory: true);
      } else if (!parentEntry.isDirectory) {
        // This is an error: a file exists where a directory is needed in the hierarchy.
        throw FileSystemError_NotADirectory(message: 'Not a directory: $parentPath');
      }
    }
  }

  // Strict check: ensures parent directory exists and is a directory, throws if not.
  // Used by mkdir non-recursive.
  void _ensureParentDirExists(RawPath path) {
    final parentPath = path.parent;
    // Check if parentPath is not null and different from the current path (to avoid issues with root)
    if (parentPath != null && parentPath.absolutePath != path.absolutePath) {
      final normalizedParent = _normalizePath(parentPath);
      final parentEntry = _entries[normalizedParent];
      if (parentEntry == null) {
        throw FileSystemError_NotFound(message: 'Path not found: $parentPath');
      }
      if (!parentEntry.isDirectory) {
        throw FileSystemError_NotADirectory(message: 'Not a directory: $parentPath');
      }
    }
    // If parentPath is null (path is likely a root or similar) or if parent exists and is a directory, it's fine.
  }

  @override
  Future<void> copyFile(RawPath sourcePath, RawPath destinationPath, CopyOptions options) async {
    final normalizedSource = _normalizePath(sourcePath);
    final normalizedDest = _normalizePath(destinationPath);

    final sourceEntry = _entries[normalizedSource];
    if (sourceEntry == null || sourceEntry.isDirectory) {
      throw io.FileSystemException('Source file not found or is a directory', sourcePath.absolutePath);
    }

    // Simulate an error if errorOnCopy is true
    if (sourceEntry.errorOnCopy) {
      throw Exception('Simulated error during copy operation');
    }

    final destEntry = _entries[normalizedDest];
    if (destEntry != null) {
      if (destEntry.isDirectory) {
        throw FileSystemError_IsADirectory(message: 'Is a directory: $destinationPath');
      } else {
        if (!options.overwriteIfExists) {
          // If it's not a directory and it exists, it must be a file.
          throw FileSystemError_AlreadyExists(message: 'File already exists: $destinationPath');
        }
      }
    }

    _ensureParentDirExists(destinationPath); // Use original destinationPath for parent check
    _entries[normalizedDest] = _FileSystemEntry(
      content: sourceEntry.content,
      createDate: sourceEntry.createDate,
      updateDate: sourceEntry.updateDate,
      isDirectory: false,
      errorOnCopy: sourceEntry.errorOnCopy,
      onDifferentPartition: sourceEntry.onDifferentPartition,
    );
  }

  @override
  Future<Stream<Path>> list(RawPath path) async {
    final normalizedPathString = _normalizePath(path);
    final entry = _entries[normalizedPathString];

    if (entry == null) {
      return Stream.error(FileSystemError_NotFound(message: 'Path not found: $path'));
    }
    if (!entry.isDirectory) {
      return Stream.error(FileSystemError_NotADirectory(message: 'Not a directory: $path'));
    }

    final children = _entries.entries.where((mapEntry) {
      final childPathString = mapEntry.key;
      if (childPathString == normalizedPathString) return false; // Not a child of itself

      // Check if childPathString is a direct child of normalizedPathString
      final parentOfChild = p.dirname(childPathString);
      return parentOfChild == normalizedPathString;
    }).map((mapEntry) {
      final childPathString = mapEntry.key;
      final childEntry = mapEntry.value;
      final childRawPath = RawPath(childPathString); // Create RawPath from the string key

      final stats = PathStats(
        type: childEntry.isDirectory ? domain.PathStatsType.directory : domain.PathStatsType.file,
        createTime: childEntry.createDate,
        updateTime: childEntry.updateDate,
        accessTime: childEntry.updateDate,
        size: childEntry.isDirectory ? null : childEntry.content?.length,
        // error: childEntry.errorOnAccess ? Exception('Simulated access error') : null, // Removed usage of non-existent field
      );
      return Path(childRawPath, stats); // Construct Path
    });

    return Stream.fromIterable(children);
  }

  @override
  Future<void> mkdir(RawPath path, {bool recursive = false}) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry != null) {
      // If entry exists, it's an error, regardless of whether it's a file or directory.
      // The tests expect FileAlreadyExistsError in both cases for mkdir.
      throw FileSystemError_AlreadyExists(message: 'Already exists: $path');
    }

    if (recursive) {
      RawPath currentPath = path;
      while (currentPath.parent != null && currentPath.absolutePath != currentPath.parent!.absolutePath) {
        final parentPath = currentPath.parent!;
        final normalizedParent = _normalizePath(parentPath);
        final parentEntry = _entries[normalizedParent];

        if (parentEntry != null && !parentEntry.isDirectory) {
          throw io.FileSystemException('Parent path exists and is not a directory', parentPath.absolutePath);
        }
        if (parentEntry == null) {
          _entries[normalizedParent] = _FileSystemEntry(isDirectory: true);
        }
        currentPath = parentPath;
      }
    } else {
      // If not recursive, ensure parent exists. The _ensureParentDirExists method will throw if not.
      _ensureParentDirExists(path);
    }

    _entries[normalizedPath] = _FileSystemEntry(isDirectory: true);
  }

  @override
  Future<String> readFile(RawPath path) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      throw FileSystemError_NotFound(message: 'Path not found: $path');
    }

    if (entry.isDirectory) {
      throw FileSystemError_IsADirectory(message: 'Is a directory: $path');
    }

    return String.fromCharCodes(entry.content!);
  }

  @override
  Future<void> rename(RawPath sourcePath, RawPath destinationPath) async {
    final normalizedSource = _normalizePath(sourcePath);
    final normalizedDest = _normalizePath(destinationPath);

    final sourceEntry = _entries[normalizedSource];
    if (sourceEntry == null) {
      throw FileSystemError_NotFound(message: 'Path not found: $sourcePath');
    }

    // Check if the file is on a different partition
    if (sourceEntry.onDifferentPartition) {
      throw FileSystemError_CrossesDevices(message: 'Crosses devices: $sourcePath');
    }

    final destEntry = _entries[normalizedDest];
    if (destEntry != null) {
      throw FileSystemError_AlreadyExists(message: 'File already exists: $destinationPath');
    }

    _ensureParentDirExists(destinationPath); // Use original destinationPath for parent check
    _entries[normalizedDest] = sourceEntry;
    _entries.remove(normalizedSource);
  }

  @override
  Future<void> rmdir(RawPath path, {bool recursive = false}) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      throw FileSystemError_NotFound(message: 'Path not found: $path');
    }

    if (!entry.isDirectory) {
      throw FileSystemError_NotFound(message: 'Path not found: $path');
    }

    // Check if directory is empty if not recursive
    if (!recursive) {
      final children = _entries.keys.where((p) => p.startsWith('$normalizedPath/') && p != normalizedPath).toList();
      if (children.isNotEmpty) {
        throw FileSystemError_DirectoryNotEmpty(message: 'Directory not empty: $path');
      }
    }

    // Remove the directory and its children if recursive
    _entries.removeWhere((p, e) => p == normalizedPath || (recursive && p.startsWith('$normalizedPath/')));
  }

  @override
  Future<void> setTimestamps(RawPath path, {DateTime? createTime, DateTime? modifyTime, DateTime? accessTime}) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      throw FileSystemError_NotFound(message: 'Path not found: $path');
    }

    _entries[normalizedPath] = _FileSystemEntry(
      content: entry.content,
      createDate: createTime,
      updateDate: modifyTime,
      isDirectory: entry.isDirectory,
    );
  }

  @override
  Future<Path> stat(RawPath path, {domain.PathStatsType fallbackType = domain.PathStatsType.unknown}) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      // Return a fake Path with unknown type for non-existent paths
      return Path(
        path, // Use the RawPath object as positional argument
        PathStats(
          // Create PathStats
          type: domain.PathStatsType.unknown,
          createTime: zeroDate,
          updateTime: zeroDate,
          accessTime: zeroDate,
          size: null,
        ),
      );
    }

    // Return a fake Path object with basic stats
    return Path(
      path, // Use the RawPath object as positional argument
      PathStats(
        // Create PathStats
        type: entry.isDirectory ? domain.PathStatsType.directory : domain.PathStatsType.file,
        createTime: entry.createDate,
        updateTime: entry.updateDate,
        accessTime: entry.updateDate,
        size: entry.isDirectory ? 0 : entry.content?.length ?? 0,
      ),
    );
  }

  @override
  Future<void> deleteFile(RawPath path) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry == null) {
      // File doesn't exist, do nothing
      return;
    }

    if (entry.isDirectory) {
      throw FileSystemError_IsADirectory(message: 'Is a directory: $path');
    }

    _entries.remove(normalizedPath);
  }

  @override
  Future<void> writeFile(RawPath path, String content) async {
    final normalizedPath = _normalizePath(path);
    final entry = _entries[normalizedPath];

    if (entry != null) {
      if (entry.isDirectory) {
        throw FileSystemError_IsADirectory(message: 'Is a directory: $path');
      }
      // Actually remove the existing entry when overwriteIfExists is true
      _entries.remove(normalizedPath);
    }

    _createParentDirHierarchyIfNeeded(path); // Ensure parent hierarchy exists or is created

    _entries[normalizedPath] = _FileSystemEntry(
      content: Uint8List.fromList(content.codeUnits),
      createDate: DateTime.now(), // Simulate creation date
      updateDate: DateTime.now(), // Simulate update date
      isDirectory: false,
    );
  }

  // Helper method to add entries to the fake file system for testing setup
  void addEntry(RawPath path, {bool isDirectory = false, String? content, bool errorOnCopy = false, bool onDifferentPartition = false}) {
    final normalizedPath = _normalizePath(path);
    _createParentDirHierarchyIfNeeded(path); // Ensure parent hierarchy exists or is created
    _entries[normalizedPath] = _FileSystemEntry(
      isDirectory: isDirectory,
      content: content != null ? Uint8List.fromList(content.codeUnits) : null,
      createDate: DateTime.now(),
      updateDate: DateTime.now(),
      errorOnCopy: errorOnCopy,
      onDifferentPartition: onDifferentPartition,
    );
  }

  // Helper method to remove entries from the fake file system for testing
  void removeEntry(RawPath path) {
    final normalizedPath = _normalizePath(path);
    _entries.remove(normalizedPath);
  }

  // Helper method to clear the fake file system
  void clear() {
    _entries.clear();
  }

  @override
  Future<CopyWithProgressOperation> copyFileWithProgress(RawPath source, RawPath dest, CopyOptions options, FutureOr<void> Function(ProgressReport) onProgress, {int chunkSize = 1024 * 1024}) {
    // TODO: Implement mock for pausable copy or throw UnimplementedError
    throw UnimplementedError('copyFileWithProgress is not implemented in InMemoryFileSystem');
  }
}

/// Represents an entry in the fake file system.
class _FileSystemEntry {
  final bool isDirectory;
  final Uint8List? content;
  final DateTime createDate;
  final DateTime updateDate;
  final bool errorOnCopy;
  final bool onDifferentPartition;

  _FileSystemEntry({
    required this.isDirectory,
    this.content,
    DateTime? createDate,
    DateTime? updateDate,
    this.errorOnCopy = false,
    this.onDifferentPartition = false,
  })  : createDate = createDate ?? DateTime.now(),
        updateDate = updateDate ?? DateTime.now();
}

// Zero date for unknown files
final DateTime zeroDate = DateTime.utc(1970, 1, 1);
