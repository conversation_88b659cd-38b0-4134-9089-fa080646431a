import 'package:flutter/foundation.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:mek_data_class/mek_data_class.dart';

import '../../app/persistence/data_repository.dart';
import '../../app/util/json_serializer.dart';
import '../../app/util/log_utils.dart';
import 'command_id.dart';
import 'keybind.dart';

part '.gen/keybind_repository.g.dart';

/// Repository for managing keybinds
class KeyBindRepository extends ChangeNotifier {
  KeyBindsData _keyBindsData = const KeyBindsData();

  KeyBindRepository._();

  static Future<KeyBindRepository> create(DataRepository dataRepository) async {
    final repository = KeyBindRepository._();
    await dataRepository.register<KeyBindsData>(
      name: "keybindings",
      createDefault: repository._createDefault,
      snapshot: repository._snapshot,
      restore: repository._restore,
      serializer: KeyBindsData.serializer,
    );
    return repository;
  }

  /// Get all keybinds
  List<KeyBind> get keybinds {
    if (kDebugMode) {
      logger.fine('Keybinds: ${_keyBindsData.keybindings.length} entries');
      for (final keybind in _keyBindsData.keybindings) {
        logger.fine('  $keybind');
      }
    }
    return _keyBindsData.keybindings;
  }

  /// Add a keybind
  void addKeybind(KeyBind keybind) {
    final keybindings = List<KeyBind>.from(_keyBindsData.keybindings);
    keybindings.add(keybind);
    _keyBindsData = _keyBindsData.copyWith(keybindings: keybindings);
    notifyListeners();
  }

  /// Remove a keybind
  void removeKeybind(KeyBind keybind) {
    final keybindings = List<KeyBind>.from(_keyBindsData.keybindings);
    keybindings.remove(keybind);
    _keyBindsData = _keyBindsData.copyWith(keybindings: keybindings);
    notifyListeners();
  }

  /// Update all keybinds
  void updateKeybinds(List<KeyBind> keybindings) {
    _keyBindsData = _keyBindsData.copyWith(keybindings: keybindings);
    notifyListeners();
  }

  KeyBindsData _createDefault() {
    if (kDebugMode) {
      logger.info('_createDefault(): Creating default keybinds');
    }
    return KeyBindsData(keybindings: [
      KeyBind(commandId: CommandId.showCommandPalette, key: 'ctrl+shift+p'),
      KeyBind(commandId: CommandId.renameFile, key: 'f2'),
      KeyBind(commandId: CommandId.copyFiles, key: 'f5'),
      KeyBind(commandId: CommandId.copyFiles, key: 'ctrl+c'),
      KeyBind(commandId: CommandId.moveFiles, key: 'f6'),
      KeyBind(commandId: CommandId.moveFiles, key: 'ctrl+x'),
      KeyBind(commandId: CommandId.deleteFiles, key: 'delete'),
      KeyBind(commandId: CommandId.navigateToParent, key: 'backspace'),
      KeyBind(commandId: CommandId.navigateUp, key: 'up'),
      KeyBind(commandId: CommandId.navigateDown, key: 'down'),
      KeyBind(commandId: CommandId.navigateBack, key: 'alt+left'),
      KeyBind(commandId: CommandId.navigateForward, key: 'alt+right'),
      KeyBind(commandId: CommandId.switchPane, key: 'tab'),
      KeyBind(commandId: CommandId.executeFile, key: 'enter'),
      KeyBind(commandId: CommandId.toggleSelection, key: 'space'),
      KeyBind(commandId: CommandId.refreshDirectory, key: 'ctrl+f'),
    ]);
  }

  KeyBindsData _snapshot() => _keyBindsData;

  void _restore(KeyBindsData snapshot) {
    _keyBindsData = snapshot;
    notifyListeners();
  }

  @override
  @visibleForTesting
  void dispose() {
    _keyBindsData = const KeyBindsData();
    super.dispose();
  }

  static final logger = loggerFor(KeyBindRepository, Level.INFO);
}

@DataClass()
@JsonSerializable()
class KeyBindsData with _$KeyBindsData {
  final List<KeyBind> keybindings;

  const KeyBindsData({this.keybindings = const []});

  factory KeyBindsData.fromJson(Map<String, dynamic> json) => _$KeyBindsDataFromJson(json);

  Map<String, dynamic> toJson() => _$KeyBindsDataToJson(this);

  static final serializer =
      JsonSerializer<KeyBindsData>(toJson: (instance) => _$KeyBindsDataToJson(instance), fromJson: (json) => _$KeyBindsDataFromJson(json));

  @override
  KeyBindsData copyWith({List<KeyBind>? keybindings}) {
    return KeyBindsData(
      keybindings: keybindings ?? this.keybindings,
    );
  }
}
